from typing import Optional

import pandas as pd
import requests


def get_fx_daily_data(
        from_symbol: str,
        to_symbol: str,
        api_key: str,
        outputsize: str = "full"
) -> Optional[pd.DataFrame]:
    """
    获取外汇日线数据

    Args:
        from_symbol: 源货币符号 (如 'EUR')
        to_symbol: 目标货币符号 (如 'USD')
        api_key: AlphaVantage API密钥
        outputsize: 数据大小 ('compact' 或 'full')

    Returns:
        包含外汇数据的DataFrame，如果请求失败则返回None
    """
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "FX_DAILY",
        "from_symbol": from_symbol,
        "to_symbol": to_symbol,
        "apikey": api_key,
        "outputsize": outputsize,
        "datatype": "json"
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # 检查HTTP错误
        data = response.json()

        # 检查API响应是否包含错误信息
        if "Error Message" in data:
            print(f"API错误: {data['Error Message']}")
            return None

        if "Note" in data:
            print(f"API提示: {data['Note']}")
            return None

        if "Time Series FX (Daily)" not in data:
            print("响应中未找到外汇数据")
            return None

        # 解析数据
        df = pd.DataFrame.from_dict(data["Time Series FX (Daily)"], orient="index")
        df = df.rename(columns={
            "1. open": "open",
            "2. high": "high",
            "3. low": "low",
            "4. close": "close"
        })

        df.index = pd.to_datetime(df.index)
        df = df.astype(float)
        df = df.sort_index()  # 按日期排序

        return df

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"数据处理错误: {e}")
        return None


from datetime import datetime


def get_utc_timestamp_of_day(year, month, day):
    """
    获取指定日期在 UTC 时区 00:00:00 的时间戳（秒级）
    """
    dt = datetime(year, month, day, 0, 0, 0, tzinfo=timezone.utc)
    return int(dt.timestamp() * 1000)


if __name__ == "__main__":
    from datetime import datetime, timezone

    # 获取当前的 UTC 时间
    now_utc = datetime.now(timezone.utc)
    # 转换为时间戳（秒，浮点数）
    timestamp = now_utc.timestamp()
    # 输出结果
    print("当前 UTC 时间:", now_utc.strftime("%Y-%m-%d %H:%M:%S %Z"))
    print("UTC 时间戳（毫秒）:", int(timestamp * 1000))

    utc_time = datetime.fromtimestamp(1754871360000 / 1000)
    print("时间:", utc_time.strftime("%Y-%m-%d %H:%M:%S %Z"))

    print(get_utc_timestamp_of_day(2025, 8, 1))
