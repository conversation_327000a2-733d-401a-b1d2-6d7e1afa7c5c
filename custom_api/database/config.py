"""
数据库配置模块

提供数据库连接配置和环境管理
"""

import os
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """数据库配置类"""
    
    # 数据库类型
    database_type: str = "sqlite"
    
    # SQLite 配置
    sqlite_path: str = "data/trading_system.db"
    
    # MySQL 配置
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_username: str = "root"
    mysql_password: str = ""
    mysql_database: str = "trading_system"
    mysql_charset: str = "utf8mb4"
    
    # PostgreSQL 配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_username: str = "postgres"
    postgres_password: str = ""
    postgres_database: str = "trading_system"
    
    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    
    # SQLAlchemy 配置
    echo: bool = False
    echo_pool: bool = False
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        
        if self.database_type.lower() == "sqlite":
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.sqlite_path), exist_ok=True)
            return f"sqlite:///{self.sqlite_path}"
        
        elif self.database_type.lower() == "mysql":
            return (
                f"mysql+pymysql://{self.mysql_username}:{self.mysql_password}"
                f"@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}"
                f"?charset={self.mysql_charset}"
            )
        
        elif self.database_type.lower() == "postgresql":
            return (
                f"postgresql+psycopg2://{self.postgres_username}:{self.postgres_password}"
                f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_database}"
            )
        
        else:
            raise ValueError(f"不支持的数据库类型: {self.database_type}")
    
    def get_engine_kwargs(self) -> Dict[str, Any]:
        """获取数据库引擎参数"""
        
        kwargs = {
            "echo": self.echo,
            "echo_pool": self.echo_pool,
        }
        
        # SQLite 不支持连接池
        if self.database_type.lower() != "sqlite":
            kwargs.update({
                "pool_size": self.pool_size,
                "max_overflow": self.max_overflow,
                "pool_timeout": self.pool_timeout,
                "pool_recycle": self.pool_recycle,
            })
        
        return kwargs


class ConfigManager:
    """配置管理器"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._config = self._load_config()
    
    def _load_config(self) -> DatabaseConfig:
        """加载配置"""
        
        # 从环境变量加载配置
        config = DatabaseConfig()
        
        # 数据库类型
        config.database_type = os.getenv("DB_TYPE", config.database_type)
        
        # SQLite 配置
        config.sqlite_path = os.getenv("SQLITE_PATH", config.sqlite_path)
        
        # MySQL 配置
        config.mysql_host = os.getenv("MYSQL_HOST", config.mysql_host)
        config.mysql_port = int(os.getenv("MYSQL_PORT", config.mysql_port))
        config.mysql_username = os.getenv("MYSQL_USERNAME", config.mysql_username)
        config.mysql_password = os.getenv("MYSQL_PASSWORD", config.mysql_password)
        config.mysql_database = os.getenv("MYSQL_DATABASE", config.mysql_database)
        config.mysql_charset = os.getenv("MYSQL_CHARSET", config.mysql_charset)
        
        # PostgreSQL 配置
        config.postgres_host = os.getenv("POSTGRES_HOST", config.postgres_host)
        config.postgres_port = int(os.getenv("POSTGRES_PORT", config.postgres_port))
        config.postgres_username = os.getenv("POSTGRES_USERNAME", config.postgres_username)
        config.postgres_password = os.getenv("POSTGRES_PASSWORD", config.postgres_password)
        config.postgres_database = os.getenv("POSTGRES_DATABASE", config.postgres_database)
        
        # 连接池配置
        config.pool_size = int(os.getenv("DB_POOL_SIZE", config.pool_size))
        config.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", config.max_overflow))
        config.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", config.pool_timeout))
        config.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", config.pool_recycle))
        
        # SQLAlchemy 配置
        config.echo = os.getenv("DB_ECHO", "false").lower() == "true"
        config.echo_pool = os.getenv("DB_ECHO_POOL", "false").lower() == "true"
        
        return config
    
    @property
    def config(self) -> DatabaseConfig:
        """获取配置"""
        return self._config
    
    def reload_config(self):
        """重新加载配置"""
        self._config = self._load_config()


# 全局配置实例
config_manager = ConfigManager()


def get_database_config() -> DatabaseConfig:
    """获取数据库配置"""
    return config_manager.config


def get_database_url() -> str:
    """获取数据库连接URL"""
    return get_database_config().get_database_url()


def get_engine_kwargs() -> Dict[str, Any]:
    """获取数据库引擎参数"""
    return get_database_config().get_engine_kwargs()


# 环境配置示例
DEVELOPMENT_CONFIG = {
    "DB_TYPE": "sqlite",
    "SQLITE_PATH": "data/dev_trading_system.db",
    "DB_ECHO": "true"
}

PRODUCTION_CONFIG = {
    "DB_TYPE": "mysql",
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": "3306",
    "MYSQL_USERNAME": "trading_user",
    "MYSQL_PASSWORD": "secure_password",
    "MYSQL_DATABASE": "trading_system_prod",
    "DB_POOL_SIZE": "20",
    "DB_MAX_OVERFLOW": "30",
    "DB_ECHO": "false"
}

TEST_CONFIG = {
    "DB_TYPE": "sqlite",
    "SQLITE_PATH": ":memory:",
    "DB_ECHO": "false"
}
