"""
PendingOrder DAO

提供订单相关的数据访问操作
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import SQLAlchemyError

from .base_dao import BaseDAO
from custom_api.database.connection import get_db_session
from custom_api.models.pending_order import (
    PendingOrder, OrderStatus, OrderSide, OrderType, 
    EffectType, TimeInForce, MarketType
)

logger = logging.getLogger(__name__)


class PendingOrderDAO(BaseDAO[PendingOrder, Dict[str, Any], Dict[str, Any]]):
    """订单数据访问对象"""
    
    def __init__(self):
        super().__init__(PendingOrder)
    
    def get_by_order_id(self, order_id: str, session: Optional[Session] = None) -> Optional[PendingOrder]:
        """
        根据订单ID获取订单
        
        Args:
            order_id: 订单ID
            session: 数据库会话
            
        Returns:
            订单实例或None
        """
        return self.get_by_field('order_id', order_id, session)
    
    def get_by_external_order_id(self, external_order_id: str, 
                                 session: Optional[Session] = None) -> Optional[PendingOrder]:
        """
        根据外部订单ID获取订单
        
        Args:
            external_order_id: 外部订单ID
            session: 数据库会话
            
        Returns:
            订单实例或None
        """
        return self.get_by_field('external_order_id', external_order_id, session)
    
    def get_orders_by_symbol(self, symbol: str, status: Optional[OrderStatus] = None,
                            skip: int = 0, limit: int = 100,
                            session: Optional[Session] = None) -> List[PendingOrder]:
        """
        根据合约获取订单列表
        
        Args:
            symbol: 合约代码
            status: 订单状态（可选）
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_orders_by_symbol(db_session: Session) -> List[PendingOrder]:
            try:
                query = db_session.query(PendingOrder).filter(PendingOrder.symbol == symbol)
                
                if status:
                    query = query.filter(PendingOrder.status == status)
                
                result = query.order_by(desc(PendingOrder.create_time)).offset(skip).limit(limit).all()
                
                logger.debug(f"查询合约{symbol}的订单: 状态={status}, 结果数量={len(result)}")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"查询合约订单失败: {e}")
                raise
        
        if session:
            return _get_orders_by_symbol(session)
        else:
            with get_db_session() as db_session:
                return _get_orders_by_symbol(db_session)
    
    def get_orders_by_status(self, status: OrderStatus, skip: int = 0, limit: int = 100,
                            session: Optional[Session] = None) -> List[PendingOrder]:
        """
        根据状态获取订单列表
        
        Args:
            status: 订单状态
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_orders_by_status(db_session: Session) -> List[PendingOrder]:
            try:
                result = (db_session.query(PendingOrder)
                         .filter(PendingOrder.status == status)
                         .order_by(desc(PendingOrder.create_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                
                logger.debug(f"查询状态为{status}的订单: 结果数量={len(result)}")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"查询状态订单失败: {e}")
                raise
        
        if session:
            return _get_orders_by_status(session)
        else:
            with get_db_session() as db_session:
                return _get_orders_by_status(db_session)
    
    def get_orders_by_channel(self, channel_code: str, status: Optional[OrderStatus] = None,
                             skip: int = 0, limit: int = 100,
                             session: Optional[Session] = None) -> List[PendingOrder]:
        """
        根据渠道获取订单列表
        
        Args:
            channel_code: 渠道代码
            status: 订单状态（可选）
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_orders_by_channel(db_session: Session) -> List[PendingOrder]:
            try:
                query = db_session.query(PendingOrder).filter(PendingOrder.channel_code == channel_code)
                
                if status:
                    query = query.filter(PendingOrder.status == status)
                
                result = query.order_by(desc(PendingOrder.create_time)).offset(skip).limit(limit).all()
                
                logger.debug(f"查询渠道{channel_code}的订单: 状态={status}, 结果数量={len(result)}")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"查询渠道订单失败: {e}")
                raise
        
        if session:
            return _get_orders_by_channel(session)
        else:
            with get_db_session() as db_session:
                return _get_orders_by_channel(db_session)
    
    def get_orders_by_time_range(self, start_time: datetime, end_time: datetime,
                                status: Optional[OrderStatus] = None,
                                skip: int = 0, limit: int = 100,
                                session: Optional[Session] = None) -> List[PendingOrder]:
        """
        根据时间范围获取订单列表
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status: 订单状态（可选）
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_orders_by_time_range(db_session: Session) -> List[PendingOrder]:
            try:
                query = (db_session.query(PendingOrder)
                        .filter(PendingOrder.create_time.between(start_time, end_time)))
                
                if status:
                    query = query.filter(PendingOrder.status == status)
                
                result = query.order_by(desc(PendingOrder.create_time)).offset(skip).limit(limit).all()
                
                logger.debug(f"查询时间范围{start_time}-{end_time}的订单: 状态={status}, 结果数量={len(result)}")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"查询时间范围订单失败: {e}")
                raise
        
        if session:
            return _get_orders_by_time_range(session)
        else:
            with get_db_session() as db_session:
                return _get_orders_by_time_range(db_session)
    
    def update_order_status(self, order_id: str, status: OrderStatus, 
                           error_message: Optional[str] = None,
                           filled_quantity: Optional[float] = None,
                           session: Optional[Session] = None) -> bool:
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            status: 新状态
            error_message: 错误信息（可选）
            filled_quantity: 成交数量（可选）
            session: 数据库会话
            
        Returns:
            是否更新成功
        """
        def _update_order_status(db_session: Session) -> bool:
            try:
                order = db_session.query(PendingOrder).filter(PendingOrder.order_id == order_id).first()
                
                if not order:
                    logger.warning(f"订单不存在: {order_id}")
                    return False
                
                # 更新状态
                order.status = status
                
                # 更新错误信息
                if error_message:
                    order.error_message = error_message
                
                # 更新成交数量
                if filled_quantity is not None:
                    order.filled_quantity = filled_quantity
                
                # 更新时间戳
                if status == OrderStatus.FILLED:
                    order.fill_time = datetime.now()
                elif status in [OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]:
                    order.fill_time = None
                
                # 更新版本号
                order.version += 1
                order.update_time = datetime.now()
                
                db_session.flush()
                
                logger.debug(f"更新订单状态成功: {order_id} -> {status}")
                return True
                
            except SQLAlchemyError as e:
                logger.error(f"更新订单状态失败: {e}")
                raise
        
        if session:
            return _update_order_status(session)
        else:
            with get_db_session() as db_session:
                return _update_order_status(db_session)
    
    def get_statistics(self, start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取订单统计信息
        
        Args:
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）
            session: 数据库会话
            
        Returns:
            统计信息字典
        """
        def _get_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                query = db_session.query(PendingOrder)
                
                # 应用时间过滤
                if start_time:
                    query = query.filter(PendingOrder.create_time >= start_time)
                if end_time:
                    query = query.filter(PendingOrder.create_time <= end_time)
                
                # 总订单数
                total_orders = query.count()
                
                # 按状态统计
                status_stats = {}
                for status in OrderStatus:
                    count = query.filter(PendingOrder.status == status).count()
                    status_stats[status.value] = count
                
                # 按合约统计（前10）
                symbol_stats = (query.with_entities(PendingOrder.symbol, func.count(PendingOrder.id))
                               .group_by(PendingOrder.symbol)
                               .order_by(desc(func.count(PendingOrder.id)))
                               .limit(10)
                               .all())
                
                # 按渠道统计
                channel_stats = (query.filter(PendingOrder.channel_code.isnot(None))
                               .with_entities(PendingOrder.channel_code, func.count(PendingOrder.id))
                               .group_by(PendingOrder.channel_code)
                               .order_by(desc(func.count(PendingOrder.id)))
                               .all())
                
                statistics = {
                    'total_orders': total_orders,
                    'status_distribution': status_stats,
                    'top_symbols': [{'symbol': symbol, 'count': count} for symbol, count in symbol_stats],
                    'channel_distribution': [{'channel': channel, 'count': count} for channel, count in channel_stats],
                    'query_time_range': {
                        'start_time': start_time.isoformat() if start_time else None,
                        'end_time': end_time.isoformat() if end_time else None
                    }
                }
                
                logger.debug(f"获取订单统计信息成功: 总数={total_orders}")
                return statistics
                
            except SQLAlchemyError as e:
                logger.error(f"获取订单统计信息失败: {e}")
                raise
        
        if session:
            return _get_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_statistics(db_session)


# 全局DAO实例
pending_order_dao = PendingOrderDAO()


def get_pending_order_dao() -> PendingOrderDAO:
    """获取订单DAO实例"""
    return pending_order_dao
