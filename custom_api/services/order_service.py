"""
订单服务层

提供订单相关的业务逻辑和高级操作
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..database import get_db_session, get_pending_order_dao
from ..models.pending_order import PendingOrder, OrderStatus, OrderSide
from ..to_order import to_order

logger = logging.getLogger(__name__)


class OrderService:
    """订单服务类"""
    
    def __init__(self):
        self.dao = get_pending_order_dao()
    
    def create_order_from_to_order_params(self, **kwargs) -> Optional[str]:
        """
        从 to_order 参数创建订单并提交
        
        Args:
            **kwargs: to_order 函数参数
            
        Returns:
            订单ID或None
        """
        try:
            with get_db_session() as session:
                # 创建订单记录
                order = PendingOrder.from_to_order_params(**kwargs)
                order = self.dao.create(order.to_dict(), session)
                
                logger.info(f"创建订单记录成功: {order.order_id}")
                
                try:
                    # 调用 to_order 函数提交订单
                    external_order_id = to_order(**kwargs)
                    
                    if external_order_id and external_order_id != '0':
                        # 更新外部订单ID和状态
                        order.external_order_id = external_order_id
                        order.status = OrderStatus.PENDING
                        order.submit_time = datetime.now()
                        
                        logger.info(f"订单提交成功: {order.order_id} -> {external_order_id}")
                    else:
                        # 订单提交失败
                        order.status = OrderStatus.REJECTED
                        order.error_message = "订单提交失败"
                        
                        logger.warning(f"订单提交失败: {order.order_id}")
                    
                    session.commit()
                    return order.order_id
                    
                except Exception as e:
                    # 订单提交异常
                    order.status = OrderStatus.REJECTED
                    order.error_message = str(e)
                    session.commit()
                    
                    logger.error(f"订单提交异常: {order.order_id}, 错误: {e}")
                    return None
                    
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None
    
    def get_order_by_id(self, order_id: str) -> Optional[PendingOrder]:
        """
        根据订单ID获取订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            订单实例或None
        """
        return self.dao.get_by_order_id(order_id)
    
    def get_orders_by_symbol(self, symbol: str, status: Optional[OrderStatus] = None,
                            page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        根据合约获取订单列表（分页）
        
        Args:
            symbol: 合约代码
            status: 订单状态（可选）
            page: 页码（从1开始）
            page_size: 每页大小
            
        Returns:
            包含订单列表和分页信息的字典
        """
        skip = (page - 1) * page_size
        orders = self.dao.get_orders_by_symbol(symbol, status, skip, page_size)
        
        # 获取总数（用于分页）
        with get_db_session() as session:
            total_query = session.query(PendingOrder).filter(PendingOrder.symbol == symbol)
            if status:
                total_query = total_query.filter(PendingOrder.status == status)
            total_count = total_query.count()
        
        return {
            'orders': [order.to_dict() for order in orders],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        }
    
    def get_pending_orders(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取待处理订单列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            包含订单列表和分页信息的字典
        """
        skip = (page - 1) * page_size
        orders = self.dao.get_orders_by_status(OrderStatus.PENDING, skip, page_size)
        
        with get_db_session() as session:
            total_count = session.query(PendingOrder).filter(
                PendingOrder.status == OrderStatus.PENDING
            ).count()
        
        return {
            'orders': [order.to_dict() for order in orders],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        }
    
    def update_order_status(self, order_id: str, status: OrderStatus,
                           error_message: Optional[str] = None,
                           filled_quantity: Optional[float] = None) -> bool:
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            status: 新状态
            error_message: 错误信息
            filled_quantity: 成交数量
            
        Returns:
            是否更新成功
        """
        return self.dao.update_order_status(order_id, status, error_message, filled_quantity)
    
    def cancel_order(self, order_id: str, reason: str = "用户取消") -> bool:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            reason: 取消原因
            
        Returns:
            是否取消成功
        """
        order = self.dao.get_by_order_id(order_id)
        if not order:
            logger.warning(f"订单不存在: {order_id}")
            return False
        
        if order.status not in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]:
            logger.warning(f"订单状态不允许取消: {order_id}, 状态: {order.status}")
            return False
        
        # 这里可以添加调用外部系统取消订单的逻辑
        # cancel_external_order(order.external_order_id)
        
        return self.dao.update_order_status(order_id, OrderStatus.CANCELLED, reason)
    
    def get_order_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        获取订单统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            统计信息字典
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        return self.dao.get_statistics(start_time, end_time)
    
    def get_recent_orders(self, hours: int = 24, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近的订单
        
        Args:
            hours: 小时数
            limit: 限制数量
            
        Returns:
            订单列表
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        orders = self.dao.get_orders_by_time_range(start_time, end_time, limit=limit)
        return [order.to_dict() for order in orders]
    
    def batch_update_orders(self, updates: List[Dict[str, Any]]) -> int:
        """
        批量更新订单
        
        Args:
            updates: 更新数据列表
            
        Returns:
            更新的订单数量
        """
        try:
            with get_db_session() as session:
                updated_count = 0
                
                for update_data in updates:
                    order_id = update_data.get('order_id')
                    if not order_id:
                        continue
                    
                    order = self.dao.get_by_order_id(order_id, session)
                    if order:
                        # 移除order_id，避免更新主键
                        update_fields = {k: v for k, v in update_data.items() if k != 'order_id'}
                        self.dao.update(order, update_fields, session)
                        updated_count += 1
                
                logger.info(f"批量更新订单成功: {updated_count}")
                return updated_count
                
        except Exception as e:
            logger.error(f"批量更新订单失败: {e}")
            raise
    
    def cleanup_old_orders(self, days: int = 90) -> int:
        """
        清理旧订单（软删除或归档）
        
        Args:
            days: 保留天数
            
        Returns:
            清理的订单数量
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            with get_db_session() as session:
                # 查找需要清理的订单
                old_orders = session.query(PendingOrder).filter(
                    PendingOrder.create_time < cutoff_time,
                    PendingOrder.status.in_([
                        OrderStatus.FILLED, 
                        OrderStatus.CANCELLED, 
                        OrderStatus.REJECTED,
                        OrderStatus.EXPIRED
                    ])
                ).all()
                
                # 这里可以实现归档逻辑，而不是直接删除
                # 例如：移动到历史表或标记为已归档
                
                cleaned_count = len(old_orders)
                logger.info(f"发现{cleaned_count}个可清理的旧订单")
                
                return cleaned_count
                
        except Exception as e:
            logger.error(f"清理旧订单失败: {e}")
            raise


# 全局服务实例
order_service = OrderService()


def get_order_service() -> OrderService:
    """获取订单服务实例"""
    return order_service
