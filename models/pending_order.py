"""
PendingOrder SQLAlchemy 模型定义

该模型用于存储待处理的交易订单，与 to_order 函数参数保持一致
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Enum, Boolean, 
    Text, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
import enum
from datetime import datetime

Base = declarative_base()


# 订单类型枚举 - 与 to_order 函数的 order_type 参数对应
class OrderType(int, enum.Enum):
    CLICK_ORDER = 0         # 点击单 (市价单)
    LIMIT_ORDER = 2         # 限价单
    SOR_ORDER = 15          # SOR单 (智能订单路由)
    DIRECT_LIMIT = 28       # 直通限价单
    BEST_QUOTE = 50         # 择优询价单 (外汇专用)
    STOP_LIMIT = 52         # 止损限价单
    STOP_ORDER = 54         # 止损单


# 交易方向枚举 - 与 to_order 函数的 side 参数对应
class OrderSide(str, enum.Enum):
    BUY = "B"               # 买入
    SELL = "S"              # 卖出


# 开平仓类型枚举 - 与 to_order 函数的 effect 参数对应
class EffectType(int, enum.Enum):
    NEUTRAL = 0             # 中性
    OPEN = 1                # 开仓
    CLOSE = 2               # 平仓
    CLOSE_TODAY = 3         # 平今 (外汇专用)
    CLOSE_YESTERDAY = 4     # 平昨 (外汇专用)


# 时效性枚举 - 与 to_order 函数的 time_in_force 参数对应
class TimeInForce(int, enum.Enum):
    GTC = 1                 # 撤销前一直有效
    FOK = 4                 # 极短时间全部成交，否则全部撤销
    FAK = 5                 # 极短时间成交，剩余量全部撤销
    GFD = 6                 # 当日有效
    GTD = 7                 # 指定日期前有效


# 执行市场枚举 - 与 to_order 函数的 in_out_market 参数对应
class MarketType(int, enum.Enum):
    INTERNAL = 1            # 内部市场
    EXTERNAL = 2            # 外部市场
    BOTH = 3                # 内/外部市场


# 投机套保标识枚举 - 与 to_order 函数的 hedge_flag 参数对应
class HedgeFlag(str, enum.Enum):
    NORMAL = "1"            # 普通
    SPECULATION = "2"       # 投机
    HEDGE = "3"             # 套保


# 订单状态枚举
class OrderStatus(str, enum.Enum):
    PENDING = "pending"         # 挂单中
    PARTIAL_FILLED = "partial"  # 部分成交
    FILLED = "filled"           # 完全成交
    CANCELLED = "cancelled"     # 已取消
    REJECTED = "rejected"       # 已拒绝
    EXPIRED = "expired"         # 已过期
    TRIGGERED = "triggered"     # 已触发


# 债券报价方式枚举 - 与 to_order 函数的 bond_quote_type 参数对应
class BondQuoteType(int, enum.Enum):
    CONTINUOUS = 9          # 连续匹配
    CENTRALIZED = 10        # 集中匹配


class PendingOrder(Base):
    """
    待处理订单模型
    
    该模型存储所有待处理的交易订单信息，字段设计与 to_order 函数参数保持一致
    """
    __tablename__ = "pending_orders"

    # 主键和唯一标识
    id = Column(Integer, primary_key=True, autoincrement=True, comment="内部自增ID")
    order_id = Column(String(64), unique=True, nullable=False, index=True, comment="订单唯一ID")
    
    # 基础订单信息 - 对应 to_order 必选参数
    symbol = Column(String(32), nullable=False, index=True, comment="合约唯一代码")
    side = Column(Enum(OrderSide), nullable=False, comment="交易方向")
    
    # 价格和数量信息
    price = Column(Float, nullable=True, comment="报单价格")
    quantity = Column(Float, nullable=True, comment="报单数量")
    filled_quantity = Column(Float, default=0.0, nullable=False, comment="已成交数量")
    
    # 订单类型和效果
    effect = Column(Enum(EffectType), nullable=True, comment="开平仓类型")
    order_type = Column(Enum(OrderType), default=OrderType.LIMIT_ORDER, nullable=False, comment="订单类型")
    
    # 市场和渠道参数
    in_out_market = Column(Enum(MarketType), default=MarketType.EXTERNAL, nullable=False, comment="执行市场")
    channel_code = Column(String(32), nullable=True, index=True, comment="交易渠道代码")
    
    # 时效性参数
    time_in_force = Column(Enum(TimeInForce), default=TimeInForce.GTC, nullable=False, comment="订单时效性")
    expire_time = Column(String(32), nullable=True, comment="订单过期时间")
    
    # 风控和策略参数
    hedge_flag = Column(Enum(HedgeFlag), default=HedgeFlag.NORMAL, nullable=False, comment="投机套保标识")
    intention = Column(Text, nullable=True, comment="交易意图")
    
    # 止损止盈参数
    warn_price = Column(Float, nullable=True, comment="止损预警价")
    stop_price = Column(Float, nullable=True, comment="止损价")
    stop_loss_price = Column(Float, nullable=True, comment="止损平仓价")
    take_profit_price = Column(Float, nullable=True, comment="止盈平仓价")
    
    # 日期参数 - 用于掉期交易和债券交易
    value_date = Column(String(8), nullable=True, comment="起息日/近端交割日 (yyyyMMdd)")
    maturity_date = Column(String(8), nullable=True, comment="到期日/远端交割日 (yyyyMMdd)")
    
    # 高级参数
    close_order_id = Column(String(64), nullable=True, comment="平仓订单ID (逐笔模式)")
    pos_type = Column(Integer, nullable=True, comment="逐笔模式标识")
    currency = Column(String(8), nullable=True, comment="使用货币")
    bond_quote_type = Column(Enum(BondQuoteType), default=BondQuoteType.CONTINUOUS, nullable=True, comment="债券报价方式")
    bp = Column(Integer, nullable=True, comment="容忍滑点 (bp)")
    
    # 订单状态和追踪信息
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True, comment="订单状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    external_order_id = Column(String(64), nullable=True, comment="外部系统订单ID")
    
    # 时间戳
    create_time = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    submit_time = Column(DateTime, nullable=True, comment="提交时间")
    fill_time = Column(DateTime, nullable=True, comment="成交时间")
    
    # 审计字段
    created_by = Column(String(64), nullable=True, comment="创建者")
    updated_by = Column(String(64), nullable=True, comment="更新者")
    version = Column(Integer, default=1, nullable=False, comment="版本号")
    
    # 表级约束
    __table_args__ = (
        # 索引定义
        Index('idx_symbol_status', 'symbol', 'status'),
        Index('idx_channel_status', 'channel_code', 'status'),
        Index('idx_create_time', 'create_time'),
        Index('idx_side_status', 'side', 'status'),
        
        # 检查约束
        CheckConstraint('quantity > 0', name='check_positive_quantity'),
        CheckConstraint('filled_quantity >= 0', name='check_non_negative_filled'),
        CheckConstraint('filled_quantity <= quantity', name='check_filled_not_exceed_quantity'),
        CheckConstraint('price > 0 OR price IS NULL', name='check_positive_price'),
        CheckConstraint('stop_loss_price > 0 OR stop_loss_price IS NULL', name='check_positive_stop_loss'),
        CheckConstraint('take_profit_price > 0 OR take_profit_price IS NULL', name='check_positive_take_profit'),
        CheckConstraint('version > 0', name='check_positive_version'),
        
        # 表注释
        {'comment': '待处理订单表 - 存储所有待处理的交易订单信息'}
    )
    
    def __repr__(self):
        return f"<PendingOrder(id={self.id}, order_id='{self.order_id}', symbol='{self.symbol}', side='{self.side.value}', status='{self.status.value}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value if self.side else None,
            'price': self.price,
            'quantity': self.quantity,
            'filled_quantity': self.filled_quantity,
            'effect': self.effect.value if self.effect else None,
            'order_type': self.order_type.value if self.order_type else None,
            'status': self.status.value if self.status else None,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
        }
    
    @classmethod
    def from_to_order_params(cls, **kwargs):
        """
        从 to_order 函数参数创建 PendingOrder 实例
        
        Args:
            **kwargs: to_order 函数的参数
            
        Returns:
            PendingOrder: 新的订单实例
        """
        # 生成唯一订单ID
        import uuid
        order_id = str(uuid.uuid4()).replace('-', '')[:16]
        
        return cls(
            order_id=order_id,
            symbol=kwargs.get('symbol'),
            side=OrderSide(kwargs.get('side')) if kwargs.get('side') else None,
            price=kwargs.get('price'),
            quantity=kwargs.get('quantity'),
            effect=EffectType(kwargs.get('effect')) if kwargs.get('effect') is not None else None,
            order_type=OrderType(kwargs.get('order_type', 2)),
            in_out_market=MarketType(kwargs.get('in_out_market', 2)),
            channel_code=kwargs.get('channel_code'),
            time_in_force=TimeInForce(kwargs.get('time_in_force', 1)),
            expire_time=kwargs.get('expire_time'),
            hedge_flag=HedgeFlag(kwargs.get('hedge_flag', '1')),
            intention=kwargs.get('intention'),
            warn_price=kwargs.get('warn_price'),
            stop_price=kwargs.get('stop_price'),
            stop_loss_price=kwargs.get('stop_loss_price'),
            take_profit_price=kwargs.get('take_profit_price'),
            value_date=kwargs.get('value_date'),
            maturity_date=kwargs.get('maturity_date'),
            close_order_id=kwargs.get('close_order_id'),
            pos_type=kwargs.get('pos_type'),
            currency=kwargs.get('currency'),
            bond_quote_type=BondQuoteType(kwargs.get('bond_quote_type', 9)) if kwargs.get('bond_quote_type') else None,
            bp=kwargs.get('bp'),
            status=OrderStatus.PENDING
        )
