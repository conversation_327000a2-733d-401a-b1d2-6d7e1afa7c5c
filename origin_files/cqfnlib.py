"""
quant function lib
量化交易函数公共库
"""
import quantapi.qlog as qlog
import quantapi.deal as deal
import quantapi.pos as pos
import time
import datetime
import numpy as np



version = 20250801


def cache_sltp_price(context, openid, sl_price=None, tp_price=None):
    """
    缓存止盈和止损价
    如果都为空则置为None
    :param context:
    :param openid:
    :param sl_price:
    :param tp_price:
    :return:
    """
    context.sl_id[openid] = sl_price if sl_price is not None else None  # 根据订单ID缓存止损价
    context.tp_id[openid] = tp_price if tp_price is not None else None  # 根据订单ID缓存止盈价


def open_buy(context, price=None, sl_price=None, tp_price=None):
    """
    开多单
    :param context: 回测上下文
    :param price: 挂单价,不设置的为当前市价[没有滑点]
    :param sl_price: 止损价,如果为none,自动设置为成本价±固定止损
    :param tp_price: 止盈价,如果为none,自动设置为成本价±固定止损
    :return:
    """
    open_price = context.bid if price is None else price
    openid = deal.to_order(context.param.symbol, 'B', open_price, context.param.lot, 1,
                           channel_code=context.param.source, pos_type=1)
    cache_sltp_price(context, openid, sl_price, tp_price)
    qlog.info_f("open_buy->订单号:{},价格:{},订单量:{},止盈价:{},止损价:{}", openid, open_price,
                context.param.lot, context.tp_id[openid], context.sl_id[openid])


def open_sell(context, price=None, sl_price=None, tp_price=None):
    """
    开空单
    :param context: 回测上下文
    :param price: 挂单价,不设置的为当前市价[没有滑点]
    :param sl_price: 止损价,如果为none,自动设置为成本价±固定止损
    :param tp_price: 止盈价,如果为none,自动设置为成本价±固定止损
    :return:
    """
    open_price = context.ask if price is None else price
    openid = deal.to_order(context.param.symbol, 'S', open_price, context.param.lot, 1,
                           channel_code=context.param.source, pos_type=1)
    cache_sltp_price(context, openid, sl_price, tp_price)
    qlog.info_f("open_sell->订单号:{},价格:{},订单量:{},止盈价:{},止损价:{}", openid, open_price,
                context.param.lot, context.tp_id[openid], context.sl_id[openid])


def close(context, buy_close=True, sell_close=True):
    """
    平单逻辑
    :param context:
    :param sell_close: 是否平空单
    :param buy_close: 是否平多单
    :return:
    """
    if buy_close and context.tc.buy_num > 0:
        close_buy(context)
    if sell_close and context.tc.sell_num > 0:
        close_sell(context)


def close_buy(context):
    """
    平买单
    :param context: 回测上下文
    :return:
    """
    pos_data = pos.get_ord_position(symbol=context.param.symbol)
    for pos_temp in pos_data:
        open_id = pos_temp['id']
        if open_id in context.openid_closeid.keys() and \
                context.openid_closeid[open_id]['close_id'] != 0:  # 已平仓
            continue
        if pos_temp['posSide'] == 2:  # 跳过卖单
            continue

        # 止盈或止损
        # 判断是否触发设置的止损止盈
        if open_id in context.sl_id and context.sl_id[open_id] is not None and context.bid <= context.sl_id[open_id]:
            qlog.info_f("close_buy->多头触发止损，当前市场买价{},低于订单{}的止损价{},准备止损退场...", context.bid, open_id, context.sl_id[open_id])
            close_all(context, keep_side='S')  # 止损
        if open_id in context.tp_id and context.tp_id[open_id] is not None and context.bid >= context.tp_id[open_id]:
            qlog.info_f("close_buy->多头触发止盈，当前市场买价{},高于订单{}的止盈价{},准备止盈离场...", context.bid, open_id, context.tp_id[open_id])
            close_all(context, keep_side='S')  # 止盈


def close_sell(context):
    pos_data = pos.get_ord_position(symbol=context.param.symbol)
    for pos_temp in pos_data:
        open_id = pos_temp['id']
        if open_id in context.openid_closeid.keys() and \
                context.openid_closeid[open_id]['close_id'] != 0:
            continue
        if pos_temp['posSide'] == 1:  # 跳过买单
            continue

        # 止盈或止损
        # 判断是否触发设置的止损止盈价
        if open_id in context.sl_id and context.sl_id[open_id] is not None and context.ask >= context.sl_id[open_id]:
            qlog.info_f("close_sell->空头触发止损,当前市场卖价{},高于订单{}的止损价{},准备止损退场...", context.ask, open_id, context.sl_id[open_id])
            close_all(context, keep_side='B')  # 止损
            return
        if open_id in context.tp_id and context.tp_id[open_id] is not None and context.ask <= context.tp_id[open_id]:
            qlog.info_f("close_sell->空头触发止盈,当前市场卖价{},低于订单{}的止盈价{},准备止盈退场...", context.ask, open_id, context.tp_id[open_id])
            close_all(context, keep_side='B')  # 止盈
            return


def close_all(context, keep_side=None):
    """
    关闭所有交易和委托单
    :param context:
    :param keep_side: 保留keep_side方向的单子,不平仓
    :return:
    """
    bid = context.bid
    ask = context.ask
    qlog.info_f('close_all->bid:{}, ask:{}', bid, ask)

    # 平仓
    pos_data = pos.get_ord_position(symbol=context.param.symbol)
    for pos_temp in pos_data:
        open_id = pos_temp['id']
        if open_id in context.openid_closeid.keys() and \
                context.openid_closeid[open_id]['close_id'] != 0:
            continue
        if pos_temp['posSide'] == 1:
            side = 'B'
            close_side = 'S'
            close_price = round(bid - context.slippage_close, context.digits)
        elif pos_temp['posSide'] == 2:
            side = 'S'
            close_side = 'B'
            close_price = round(ask + context.slippage_close, context.digits)
        else:
            continue
        if keep_side == side:
            continue
        quantity = pos_temp['quantity'] - pos_temp['frozenQuantity']
        if quantity > 0:
            r = deal.to_order(context.param.symbol, close_side, close_price, quantity, 2, close_order_id=open_id,
                              channel_code=context.param.source, pos_type=1, time_in_force=5)
            # 缓存平仓的open_id和close_id
            context.openid_closeid[open_id] = {'close_id': r, 'order_time': context.nowtime, 'close_side': close_side}
            context.closeid_openid[r] = open_id
            sl_price = context.sl_id.get(open_id, 'N/A')
            tp_price = context.tp_id.get(open_id, 'N/A')
            qlog.info_f('close_all->symbol:{}, close_side:{},close_price:{},sl:{},tp:{},quantity:{},open_id:{},close_id:{}',
                        context.param.symbol, close_side, close_price, sl_price, tp_price, quantity, open_id, r)


def bid_ask_cache(context, data_temp):
    """
    缓存市价
    :param context:
    :param data_temp: 行情
    :return:
    """

    context.bid = data_temp['best_bid']
    context.ask = data_temp['best_ask']


def check_spread(context, data_temp):
    """
    价差检查函数
    :param context: 上下文
    :return: None
    """
    bid_ask_cache(context, data_temp)
    context.nowtime = data_temp['time'] // 1000
    if abs(data_temp['best_ask'] - data_temp['best_bid']) > context.param.sp * context.point:
        qlog.info("点差过高,关闭开关！")
        context.all_open = False  # 关闭开关
        return False
    else:
        context.all_open = True  # 开启开关
        return True


def check_bar(context, bar):
    """
    校验bar的数量是否满足条件及时间是否更新（不建议使用，最好拆开用）
    :param context:
    :param bar:
    :return:
    """
    if not check_bar_num(context, bar):
        return False

    return check_bar_time(context, bar)


def check_bar_num(context, bar):
    """
    如果flag满足要求则不校验
    :param context:
    :param bar:
    :return:
    context.k_num_flag 要在init里面初始化
    """
    if not context.k_num_flag:
        len_bar = len(bar['close'])
        if len_bar < context.start_k_num + 1:
            # qlog.info_f("当前bar数量[{}],不满足[{}]要求", len_bar, context.start_k_num)
            return False
        else:
            context.k_num_flag = True
    else:
        return True


def check_bar_time(context, bar, new_bar_open = False):
    """
    校验时间是否更新
    :param context:
    :param bar:
    :return:
    """
    new_time = bar['time'][-2]
    # qlog.info_f("new_bar_time:{},old_bar_time:{}",new_time,context.bar_last_time)
    if context.bar_last_time == new_time:
        # qlog.info_f("当前bar未更新...")
        return False
    else:
        # 如果不相等重新赋值
        # qlog.info_f("更新bar时间戳...")
        context.bar_last_time = new_time
        context.all_open = True if new_bar_open else context.all_open  # TODO  新周期开启开关
        return True


def protect_order(context):
    '''
    推平保护公共方法
    需要在context中初始化以下量：
    context.protect_point: 触发推平保护的盈利点数
    context.protect_move_point: 触发后移动到成本价附近的点差值
    protect_point > protect_move_point
    '''

    pos_data = pos.get_ord_position()
    for pos_temp in pos_data:
        open_id = pos_temp['id']
        if open_id in context.openid_closeid.keys() and \
                context.openid_closeid[open_id]['close_id'] != 0:  # 已平仓
            continue
        sl_price_old = context.sl_id.get(pos_temp['id'])  # 旧止损价
        deal_price = pos_temp['costPrice']  # 成本价

        if pos_temp['posSide'] == 1:  # 买单
            # 止损价<成本价   当前市价-成本价 > 推平保点数
            if (sl_price_old is None or sl_price_old < deal_price) \
                    and context.bid - deal_price > context.protect_point * context.point:
                # 设置新止损价位 成本价+推平保点数  即止损价上移到成本价之上
                sl_price_new = deal_price + context.protect_move_point * context.point
                qlog.info_f("[protect_order]推平保护, 订单id:{}, 成本价:{}, 原始止损价:{}, 最新止损价:{}",
                            open_id, deal_price, sl_price_old, sl_price_new)
                context.sl_id[pos_temp['id']] = sl_price_new  # 更新止损价
        if pos_temp['posSide'] == 2:  # 卖单
            # 止损价>成本价   成本价-当前市价 > 推平保点数
            if (sl_price_old is None or sl_price_old > deal_price)\
                    and deal_price - context.ask > context.protect_point * context.point:
                # 设置新止损价位 成本价-推平保点数  即止损价下移到成本价之下
                sl_price_new = deal_price - context.protect_move_point * context.point
                qlog.info_f("[protect_order]推平保护, 订单id:{}, 成本价:{}, 原始止损价:{}, 最新止损价:{}",
                            open_id, deal_price, sl_price_old, sl_price_new)
                context.sl_id[pos_temp['id']] = sl_price_new  # 更新止损价


def moving_sl(context, clean_tp=False):
    '''
    移动止损公共方法
	:param clean_tp 移动止损价后是否清空止盈价
    需要在context中初始化以下量：
    context.is_move_sl = True  # 布尔值，是否进行移动止损（目前写死，后续或可配）
    context.start_move_sl = 250  # 开始移动止损的点位（目前写死，后续或可配）
    context.move_point_sl = 200  # 移动止损的点位（目前写死，后续或可配）
    '''
    pos_data = pos.get_ord_position(symbol=context.param.symbol)
    for pos_temp in pos_data:
        open_id = pos_temp['id']
        deal_price = pos_temp['costPrice']  # 订单成本价
        sl_price_old = context.sl_id.get(open_id)  # 取出当前订单止损价，如为None表示没有设置，参见cache_sltp_price函数
        if open_id in context.openid_closeid.keys() and \
                context.openid_closeid[open_id]['close_id'] != 0:  # 已经平过了
            continue
        if pos_temp['posSide'] == 1:  # 买单
            # 当市价高于(成本价+移动止损开启点)时，进入移动止损
            if context.bid >= deal_price + context.start_move_sl * context.point:
                # 如果当前止损点位过低或者未设置，则提升至(市价-移动止损点位)
                if sl_price_old is None or context.bid - sl_price_old > context.move_point_sl * context.point:
                    sl_price_new = context.bid - context.move_point_sl * context.point
                    if clean_tp:
                        context.tp_id[pos_temp['id']] = None  # 清空止盈价
                    tp_price = context.tp_id.get(pos_temp['id'], 'N/A')
                    qlog.info_f("[moving_sl]移动止损, 订单id:{}, 成本价:{}, 原始止损价:{}, 最新止损价:{}, 止盈价:{}",
                                open_id, deal_price, sl_price_old, sl_price_new, tp_price)
                    context.sl_id[pos_temp['id']] = sl_price_new  # 更新止损价
        if pos_temp['posSide'] == 2:  # 卖单
            # 当市价低于(成本价-移动止损开启点)时，进入移动止损
            if context.ask <= deal_price - context.start_move_sl * context.point:
                # 如果当前止损点位过高或者未设置，则降低至(市价+移动止损点位)
                if sl_price_old is None or sl_price_old - context.ask > context.move_point_sl * context.point:
                    sl_price_new = context.ask + context.move_point_sl * context.point
                    if clean_tp:
                        context.tp_id[pos_temp['id']] = None  # 清空止盈价
                    tp_price = context.tp_id.get(pos_temp['id'], 'N/A')
                    qlog.info_f("[moving_sl]移动止损, 订单id:{}, 成本价:{}, 原始止损价:{}, 最新止损价:{}, 止盈价:{}",
                                open_id, deal_price, sl_price_old, sl_price_new, tp_price)
                    context.sl_id[pos_temp['id']] = sl_price_new  # 更新止损价


class TradeCount:
    """
    统计交易类
    """

    def __init__(self, context):
        self.buy_num = 0  # 多单数量
        self.sell_num = 0  # 空单数量
        self.buy_lots_all = 0  # 多单总手数（累加）
        self.sell_lots_all = 0  # 空单总手数（累加）
        self.buy_lots_one = 0  # 多单手数（一单）
        self.sell_lots_one = 0  # 空单手数（一单）
        self.buy_prof = 0  # 多单盈亏（收益+隔夜利息+手续费）
        self.sell_prof = 0  # 空单盈亏（收益+隔夜利息+手续费）
        self.buy_avg = 0  # 多单盈亏平衡点。
        self.sell_avg = 0  # 空单盈亏平衡点
        self.buy_price = 0  # 多单开单价
        self.sell_price = 0  # 空单开单价
        self.buy_id = 0  # 多单id号
        self.sell_id = 0  # 空单id号

        # 获取全部未平持仓（逐笔模式）
        pos_data = pos.get_ord_position()

        if len(pos_data) > 0:
            # qlog.info_f("CountTrades->{}", pos_data)
            for pos_temp in pos_data:
                open_id = pos_temp['id']
                quantity = pos_temp['quantity'] - pos_temp['frozenQuantity']
                if quantity <= 0:
                    continue
                # 多头订单统计
                deal_price = pos_temp['costPrice']
                if pos_temp['posSide'] == 1:  # 多头
                    self.buy_num += 1
                    self.buy_lots_all += quantity
                    self.buy_lots_one += quantity
                    self.buy_price = deal_price
                    self.buy_id = open_id
                    self.buy_prof += pos_temp['profit']
                    if open_id not in context.sl_id and context.sl is not None:
                        context.sl_id[open_id] = round(deal_price - context.sl * context.point, context.digits)
                        qlog.info_f("TradeCount->更新止损价，open_id:{},sl:{}", open_id, context.sl_id[open_id])
                    elif open_id in context.sl_id and context.sl_id[open_id] is None and context.sl is not None:
                        context.sl_id[open_id] = round(deal_price - context.sl * context.point, context.digits)
                        qlog.info_f("TradeCount->更新止损价，open_id:{},sl:{}", open_id, context.sl_id[open_id])
                    if open_id not in context.tp_id and context.tp is not None:
                        if hasattr(context, 'isDualTp') and context.isDualTp:  # 如果采用双重止盈价，则需要比较哪个更小（能优先达到）
                            context.tp_id[open_id] = round(min(context.dual_stable_tp[0], deal_price + context.tp * context.point), context.digits)
                            qlog.info_f("采用双重止盈效果触发!按成本价止盈点{},固定止盈点{},取最小值。当前订单止盈点最终为{}",
                                        deal_price + context.tp * context.point, context.dual_stable_tp[0], context.tp_id[open_id])
                        else:  # 如果不是双重止盈，直接取成本价+止盈点数即可
                            context.tp_id[open_id] = round(deal_price + context.tp * context.point, context.digits)
                            qlog.info_f("TradeCount->更新止盈价，open_id:{},tp:{}", open_id, context.tp_id[open_id])
                    elif open_id in context.tp_id and context.tp_id[open_id] is None and context.tp is not None:
                        if hasattr(context, 'isDualTp') and context.isDualTp:  # 如果采用双重止盈价，则需要比较哪个更小（能优先达到）
                            context.tp_id[open_id] = round(min(context.dual_stable_tp[0], deal_price + context.tp * context.point), context.digits)
                            qlog.info_f("采用双重止盈效果触发!按成本价止盈点{},固定止盈点{},取最小值。当前订单止盈点最终为{}",
                                        deal_price + context.tp * context.point, context.dual_stable_tp[0], context.tp_id[open_id])
                        else:  # 如果不是双重止盈，直接取成本价+止盈点数即可
                            context.tp_id[open_id] = round(deal_price + context.tp * context.point, context.digits)
                            qlog.info_f("TradeCount->更新止盈价，open_id:{},tp:{}", open_id, context.tp_id[open_id])
                elif pos_temp['posSide'] == 2: # 空头
                    self.sell_num += 1
                    self.sell_lots_all += quantity
                    self.sell_lots_one += quantity
                    self.sell_price = deal_price
                    self.sell_id = open_id
                    self.sell_prof += pos_temp['profit']
                    if open_id not in context.sl_id and context.sl is not None:
                        context.sl_id[open_id] = round(deal_price + context.sl * context.point, context.digits)
                        qlog.info_f("TradeCount->更新止损价，open_id:{},sl:{}", open_id, context.sl_id[open_id])
                    elif open_id in context.sl_id and context.sl_id[open_id] is None and context.sl is not None:
                        context.sl_id[open_id] = round(deal_price + context.sl * context.point, context.digits)
                        qlog.info_f("TradeCount->更新止损价，open_id:{},sl:{}", open_id, context.sl_id[open_id])
                    if open_id not in context.tp_id and context.tp is not None:
                        if hasattr(context, 'isDualTp') and context.isDualTp:  # 如果采用双重止盈价，则需要比较哪个更大（能优先达到）
                            context.tp_id[open_id] = round(max(context.dual_stable_tp[1], deal_price - context.tp * context.point), context.digits)
                            qlog.info_f("采用双重止盈效果触发!按成本价止盈点{},固定止盈点{},取最大值。当前订单止盈点最终为{}",
                                        deal_price - context.tp * context.point, context.dual_stable_tp[1], context.tp_id[open_id])
                        else:  # 如果不是双重止盈，直接取成本价-止盈点数即可
                            context.tp_id[open_id] = round(deal_price - context.tp * context.point, context.digits)
                            qlog.info_f("TradeCount->更新止盈价，open_id:{},tp:{}", open_id, context.tp_id[open_id])
                    elif open_id in context.tp_id and context.tp_id[open_id] is None and context.tp is not None:
                        if hasattr(context, 'isDualTp') and context.isDualTp:  # 如果采用双重止盈价，则需要比较哪个更大（能优先达到）
                            context.tp_id[open_id] = round(max(context.dual_stable_tp[1], deal_price - context.tp * context.point), context.digits)
                            qlog.info_f("采用双重止盈效果触发!按成本价止盈点{},固定止盈点{},取最大值。当前订单止盈点最终为{}",
                                        deal_price - context.tp * context.point, context.dual_stable_tp[1], context.tp_id[open_id])
                        else:  # 如果不是双重止盈，直接取成本价-止盈点数即可
                            context.tp_id[open_id] = round(deal_price - context.tp * context.point, context.digits)
                            qlog.info_f("TradeCount->更新止盈价，open_id:{},tp:{}", open_id, context.tp_id[open_id])
            if self.buy_lots_all > 0:
                self.buy_avg = round(context.bid - (self.buy_prof / self.buy_lots_all / 1) * context.point,
                                     context.digits)
            if self.sell_lots_all > 0:
                self.sell_avg = round(context.ask + (self.sell_prof / self.sell_lots_all / 1) * context.point,
                                      context.digits)


def sl_tool(context):
    """
    止损工具函数
    :param context:
    :return:
    """
    if context.protect_flag:
        protect_order(context)
    if context.move_flag:
        moving_sl(context)


# 检查cur_time是否在start_time和end_time之间，支持跨日
def is_bussiness_time(cur_time, start_time, end_time):  # 判断交易时间,后续需要参照贵金属拓展api
    if (cur_time > start_time and cur_time < end_time) or (start_time > end_time and (cur_time > start_time or cur_time < end_time)):
        return True
    return False


# ms级时间戳转日期时间
def stamp_to_time(timestamp, format_target="%Y-%m-%d %H:%M:%S"):
    time_stamp = float(int(timestamp) / 1000)
    time_array = time.localtime(time_stamp)
    return time.strftime(format_target, time_array)


# 时间字符串格式化
def format_time(time_str, format_org="%Y%m%d%H%M%S", format_target="%Y-%m-%d %H:%M:%S"):
    dt = datetime.datetime.strptime(str(time_str), format_org)
    return dt.strftime(format_target)


def format_num(n):
    return "{:.8f}".format(n)


# -----------------------------Indicators---------------------------------

def SMMA(price, period=20):
    """
    SMMA指标计算
    参考：https://www.metatrader5.com/zh/mobile-trading/android/help/chart/indicators/trend_indicators/moving_average
    :param price: 价格数组,numpy格式
    :param period: 计算周期
    :return:  smma结算结果
    """
    price_num = len(price)
    res = np.zeros(price_num, dtype=np.float64)
    res.fill(np.NAN)
    if price_num < period:
        return res
    else:
        res[period - 1] = price[:period].mean()
        for x in range(period, price_num):
            res[x] = (res[x - 1] * (period - 1) + price[x]) / period
    return res


# 线性加权移动平均
def LWMA(data, period):
    price_num = len(data)
    res = np.zeros(price_num, dtype=np.float64)
    res.fill(np.NAN)
    if price_num < period:
        return res
    weights = np.arange(1, period + 1)
    for i in range(period-1, price_num):
        res[i] = np.sum(data[i-period+1:i+1] * weights) / np.sum(weights)
    return res


class CommonParam:
    RSRS_ORG = "org"
    RSRS_ZSCORE = "zscore"
    RSRS_RIGHTDEV = "rightdev"


# ==================== 性能监控组件 ====================
"""
性能监控组件使用说明：

1. 初始化监控：
   在策略的 init() 函数中调用：
   init_performance_monitor(context)

2. 装饰器使用：
   在需要监控的方法上添加装饰器：
   @performance_monitor('onData')
   def onData(context, data):
       # 策略逻辑
       pass

3. 定期输出统计：
   在 onData 方法中定期调用：
   if context.tick_counter % context.performance_log_interval == 0:
       output_performance_stats(context)

4. 最终总结：
   在 onMonitor 方法中调用：
   output_final_performance_summary(context)

监控指标包括：
- 整体运行时间 vs 代码执行时间
- 各方法的执行时间统计
- 性能瓶颈检测
- 效率评估和建议
"""

def performance_monitor(method_name):
    """
    性能监控装饰器
    
    使用方法：
    @performance_monitor('方法名')
    def your_method(context, *args, **kwargs):
        # 你的方法逻辑
        pass
    
    功能：
    - 自动记录方法执行时间
    - 统计方法调用次数
    - 计算平均执行时间
    - 检测性能瓶颈（超过100毫秒会发出警告）
    - 记录最近100次执行的详细时间
    
    :param method_name: 方法名称，用于统计和日志输出
    :return: 装饰器函数
    """
    def decorator(func):
        def wrapper(context, *args, **kwargs):
            start_time = time.time()
            try:
                result = func(context, *args, **kwargs)
                return result
            finally:
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000  # 转换为毫秒
                
                # 更新性能统计
                if hasattr(context, 'performance_stats'):
                    stats_key = f'{method_name}_total_time'
                    count_key = f'{method_name}_count'
                    avg_key = f'{method_name}_avg_time'
                    
                    if stats_key in context.performance_stats:
                        context.performance_stats[stats_key] += execution_time
                        context.performance_stats[count_key] += 1
                        context.performance_stats[avg_key] = context.performance_stats[stats_key] / context.performance_stats[count_key]
                    
                    # 更新代码执行总时间
                    context.performance_stats['code_execution_time'] += execution_time
                    
                    # 计算空闲时间（从上次tick到这次执行的时间间隔）
                    current_time = time.time()
                    if 'last_tick_time' in context.performance_stats:
                        idle_time = (current_time - context.performance_stats['last_tick_time']) * 1000
                        context.performance_stats['idle_time'] += idle_time
                    context.performance_stats['last_tick_time'] = current_time
                    
                    # 记录详细时间
                    if 'method_timings' not in context.performance_stats:
                        context.performance_stats['method_timings'] = {}
                    
                    if method_name not in context.performance_stats['method_timings']:
                        context.performance_stats['method_timings'][method_name] = []
                    
                    context.performance_stats['method_timings'][method_name].append({
                        'timestamp': time.time(),
                        'execution_time': execution_time,
                        'args': str(args),
                        'kwargs': str(kwargs)
                    })
                    
                    # 保持最近100条记录
                    if len(context.performance_stats['method_timings'][method_name]) > 100:
                        context.performance_stats['method_timings'][method_name] = context.performance_stats['method_timings'][method_name][-100:]
                
                # 如果执行时间超过阈值，记录警告
                if execution_time > 100:  # 超过100毫秒
                    qlog.info_f("[性能警告] {}方法执行时间过长: {:.2f}毫秒", method_name, execution_time)
        
        return wrapper
    return decorator


def init_performance_monitor(context):
    """
    初始化性能监控统计
    
    使用方法：
    在策略的 init() 函数中调用此函数来初始化性能监控组件
    
    示例：
    def init(context):
        # 其他初始化代码...
        init_performance_monitor(context)  # 初始化性能监控
    
    功能：
    - 创建性能统计数据结构
    - 初始化各种监控指标
    - 设置监控参数（如日志输出间隔）
    - 记录策略启动时间
    
    初始化的统计项：
    - onData/onOrder/onTrade 方法的执行时间统计
    - 整体运行时间和代码执行时间
    - 方法调用次数和平均执行时间
    - 最近100次执行的详细记录
    
    :param context: 策略上下文对象
    """
    context.performance_stats = {
        'onData_total_time': 0,
        'onData_count': 0,
        'onData_avg_time': 0,
        'onOrder_total_time': 0,
        'onOrder_count': 0,
        'onOrder_avg_time': 0,
        'onTrade_total_time': 0,
        'onTrade_count': 0,
        'onTrade_avg_time': 0,
        'method_timings': {},  # 记录每个方法的详细时间
        'overall_start_time': time.time(),  # 整体开始时间
        'code_execution_time': 0,  # 代码执行总时间
        'idle_time': 0,  # 空闲时间
        'last_tick_time': time.time()  # 上次tick时间
    }
    context.performance_log_interval = 500001  # 每5000次tick输出一次性能统计

    ##########  Tick统计加速器  ##########
    context.tick_counter = 0  # tick运行次数统计
    
    qlog.info_f("[性能监控] 性能监控组件初始化完成")


def output_performance_stats(context):
    """
    输出性能统计信息
    
    使用方法：
    在策略的 onData 方法中定期调用此函数来输出性能统计
    
    示例：
    def onData(context, data):
        context.tick_counter += 1
        
        # 每5000次tick输出一次性能统计
        if context.tick_counter % context.performance_log_interval == 0:
            output_performance_stats(context)
    
    输出内容：
    - 整体运行时间 vs 代码执行时间对比
    - 各方法的执行时间统计（总时间、调用次数、平均时间）
    - 最近10次执行的最大、最小、平均时间
    - 性能评估和建议（效率指标、瓶颈警告等）
    
    性能评估标准：
    - 代码执行效率 > 50%: 可能存在性能瓶颈
    - 代码执行效率 < 10%: 系统运行效率良好
    - 其他情况: 性能表现正常
    
    :param context: 策略上下文对象
    """
    if not hasattr(context, 'performance_stats'):
        return
    
    stats = context.performance_stats
    current_time = time.time()
    overall_runtime = (current_time - stats.get('overall_start_time', current_time)) * 1000  # 整体运行时间（毫秒）
    code_execution_time = stats.get('code_execution_time', 0)  # 代码执行时间（毫秒）
    idle_time = stats.get('idle_time', 0)  # 空闲时间（毫秒）
    
    # 重新计算空闲时间（整体运行时间 - 代码执行时间）
    actual_idle_time = overall_runtime - code_execution_time
    actual_idle_time = max(0, actual_idle_time)  # 确保不为负数
    
    # 计算百分比
    code_percentage = (code_execution_time / overall_runtime * 100) if overall_runtime > 0 else 0
    idle_percentage = (actual_idle_time / overall_runtime * 100) if overall_runtime > 0 else 0
    
    qlog.info_f("[性能统计] ========== 运行时间对比报告 ==========")
    qlog.info_f("[性能统计] 整体运行时间: {:.2f}分钟 ({:.2f}秒)", overall_runtime/60000, overall_runtime/1000)
    qlog.info_f("[性能统计] 代码执行时间: {:.2f}秒 ({:.2f}毫秒) - 占比: {:.2f}%", 
               code_execution_time/1000, code_execution_time, code_percentage)
    qlog.info_f("[性能统计] 后台运行时间: {:.2f}秒 ({:.2f}毫秒) - 占比: {:.2f}%", 
               actual_idle_time/1000, actual_idle_time, idle_percentage)
    qlog.info_f("[性能统计] 效率指标: 代码执行效率 {:.2f}%", code_percentage)
    
    qlog.info_f("[性能统计] ---------- 详细方法统计 ----------")
    qlog.info_f("[性能统计] onData方法 - 总执行时间: {:.2f}毫秒, 调用次数: {}, 平均时间: {:.2f}毫秒", 
               stats.get('onData_total_time', 0), stats.get('onData_count', 0), stats.get('onData_avg_time', 0))
    qlog.info_f("[性能统计] onOrder方法 - 总执行时间: {:.2f}毫秒, 调用次数: {}, 平均时间: {:.2f}毫秒", 
               stats.get('onOrder_total_time', 0), stats.get('onOrder_count', 0), stats.get('onOrder_avg_time', 0))
    qlog.info_f("[性能统计] onTrade方法 - 总执行时间: {:.2f}毫秒, 调用次数: {}, 平均时间: {:.2f}毫秒", 
               stats.get('onTrade_total_time', 0), stats.get('onTrade_count', 0), stats.get('onTrade_avg_time', 0))
    
    # 输出最近的方法执行时间
    if 'method_timings' in stats:
        qlog.info_f("[性能统计] ---------- 最近执行时间 ----------")
        for method_name, timings in stats['method_timings'].items():
            if timings:
                recent_times = [t['execution_time'] for t in timings[-100:]]  # 最近10次
                avg_recent = sum(recent_times) / len(recent_times)
                max_recent = max(recent_times)
                min_recent = min(recent_times)
                qlog.info_f("[性能统计] {}方法最近10次 - 平均: {:.2f}毫秒, 最大: {:.2f}毫秒, 最小: {:.2f}毫秒", 
                           method_name, avg_recent, max_recent, min_recent)
    
    # 性能评估
    if code_percentage > 50:
        qlog.info_f("[性能警告] 代码执行时间占比过高({:.2f}%)，可能存在性能瓶颈", code_percentage)
    elif code_percentage < 10:
        qlog.info_f("[性能提示] 代码执行时间占比较低({:.2f}%)，系统运行效率良好", code_percentage)
    else:
        qlog.info_f("[性能提示] 代码执行时间占比正常({:.2f}%)", code_percentage)
    
    qlog.info_f("[性能统计] ==========================================")


def output_final_performance_summary(context):
    """
    输出最终性能总结
    
    使用方法：
    在策略的 onMonitor 方法中调用此函数来输出最终的性能总结
    
    示例：
    def onMonitor(context, data):
        # 输出最终性能总结
        output_final_performance_summary(context)
    
    输出内容：
    - 策略总运行时间和各时间占比
    - 平均每tick处理时间
    - 各方法的调用次数和总执行时间
    - 性能评估和建议
    
    性能评估标准：
    - 平均每tick处理时间 > 50毫秒: 建议优化
    - 平均每tick处理时间 < 10毫秒: 性能良好
    - 其他情况: 处理时间正常
    
    代码执行效率评估：
    - 代码执行效率 > 50%: 考虑优化算法或减少计算量
    - 代码执行效率 < 5%: 可以考虑增加更多功能
    - 其他情况: 性能表现正常
    
    :param context: 策略上下文对象
    """
    if not hasattr(context, 'performance_stats'):
        return
    
    stats = context.performance_stats
    current_time = time.time()
    overall_runtime = (current_time - stats.get('overall_start_time', current_time)) * 1000
    code_execution_time = stats.get('code_execution_time', 0)
    idle_time = stats.get('idle_time', 0)
    
    # 重新计算空闲时间（整体运行时间 - 代码执行时间）
    actual_idle_time = overall_runtime - code_execution_time
    actual_idle_time = max(0, actual_idle_time)  # 确保不为负数
    
    # 计算百分比
    code_percentage = (code_execution_time / overall_runtime * 100) if overall_runtime > 0 else 0
    idle_percentage = (actual_idle_time / overall_runtime * 100) if overall_runtime > 0 else 0
    
    qlog.info_f("[最终总结] ========== 策略运行性能总结 ==========")
    qlog.info_f("[最终总结] 策略总运行时间: {:.2f}分钟 ({:.2f}秒)", overall_runtime/60000, overall_runtime/1000)
    qlog.info_f("[最终总结] 代码执行时间: {:.2f}秒 ({:.2f}%)", code_execution_time/1000, code_percentage)
    qlog.info_f("[最终总结] 后台运行时间: {:.2f}秒 ({:.2f}%)", actual_idle_time/1000, idle_percentage)
    qlog.info_f("[最终总结] 总tick处理次数: {}", context.tick_counter)
    
    # 计算平均性能指标
    if context.tick_counter > 0:
        avg_tick_time = code_execution_time / context.tick_counter
        qlog.info_f("[最终总结] 平均每tick处理时间: {:.2f}毫秒", avg_tick_time)
        
        if avg_tick_time > 50:
            qlog.info_f("[最终总结] 性能评估: 每tick处理时间较长，建议优化")
        elif avg_tick_time < 10:
            qlog.info_f("[最终总结] 性能评估: 每tick处理时间较短，性能良好")
        else:
            qlog.info_f("[最终总结] 性能评估: 每tick处理时间正常")
    
    # 方法调用统计
    qlog.info_f("[最终总结] ---------- 方法调用统计 ----------")
    qlog.info_f("[最终总结] onData调用次数: {}, 总执行时间: {:.2f}毫秒", 
               stats.get('onData_count', 0), stats.get('onData_total_time', 0))
    qlog.info_f("[最终总结] onOrder调用次数: {}, 总执行时间: {:.2f}毫秒", 
               stats.get('onOrder_count', 0), stats.get('onOrder_total_time', 0))
    qlog.info_f("[最终总结] onTrade调用次数: {}, 总执行时间: {:.2f}毫秒", 
               stats.get('onTrade_count', 0), stats.get('onTrade_total_time', 0))
    
    # 性能建议
    qlog.info_f("[最终总结] ---------- 性能建议 ----------")
    if code_percentage > 50:
        qlog.info_f("[最终总结] 建议: 代码执行时间占比过高，考虑优化算法或减少计算量")
    elif code_percentage < 5:
        qlog.info_f("[最终总结] 建议: 代码执行效率很高，可以考虑增加更多功能")
    else:
        qlog.info_f("[最终总结] 建议: 性能表现正常，可以继续优化")
    
    qlog.info_f("[最终总结] ==========================================")


# ==================== 完整使用示例 ====================
"""
性能监控组件完整使用示例：

# 1. 导入模块
from cqfnlib import *

# 2. 初始化函数
def init(context):
    # 其他初始化代码...
    context.param = Param()
    context.tick_counter = 0
    context.tick_start_time = time.time()
    
    # 初始化性能监控
    init_performance_monitor(context)

# 3. 使用装饰器监控方法
@performance_monitor('onData')
def onData(context, data):
    # 增加tick计数
    context.tick_counter += 1
    
    # 定期输出性能统计
    if context.tick_counter % context.performance_log_interval == 0:
        output_performance_stats(context)
    
    # 策略逻辑...
    pass

@performance_monitor('onOrder')
def onOrder(context, order):
    # 订单处理逻辑...
    pass

@performance_monitor('onTrade')
def onTrade(context, trade):
    # 成交处理逻辑...
    pass

# 4. 最终总结
def onMonitor(context, data):
    # 输出最终性能总结
    output_final_performance_summary(context)

# 5. 其他事件处理函数
def onTime(context, time, name):
    pass

def onBusinessDate(context, data):
    pass

使用效果：
- 自动监控各方法的执行时间
- 定期输出性能统计报告
- 策略结束时输出完整性能总结
- 提供性能优化建议
"""