# 数据库系统使用指南

## 概述

本项目提供了一个完整的数据库连接、会话管理和DAO层架构，支持多种数据库类型，提供了灵活的配置管理和强大的数据访问功能。

## 架构组件

### 1. 配置管理层 (`database/config.py`)
- **DatabaseConfig**: 数据库配置类
- **ConfigManager**: 配置管理器（单例模式）
- 支持环境变量配置
- 支持多种数据库类型（SQLite、MySQL、PostgreSQL）

### 2. 连接管理层 (`database/connection.py`)
- **DatabaseManager**: 数据库管理器
- 连接池管理
- 会话工厂
- 事务管理
- 自动重连机制

### 3. DAO层 (`database/base_dao.py`, `database/pending_order_dao.py`)
- **BaseDAO**: 通用数据访问基类
- **PendingOrderDAO**: 订单专用DAO
- 完整的CRUD操作
- 批量操作支持
- 复杂查询功能

### 4. 服务层 (`services/order_service.py`)
- **OrderService**: 订单业务逻辑服务
- 高级业务操作
- 与to_order函数集成
- 分页查询支持

## 快速开始

### 1. 基础使用

```python
from custom_api.database import init_database, get_db_session
from custom_api.services import get_order_service

# 初始化数据库
init_database()

# 使用服务层
service = get_order_service()

# 创建订单
order_id = service.create_order_from_to_order_params(
    symbol='EURUSD',
    side='B',
    price=1.1000,
    quantity=100000,
    effect=1,
    order_type=2
)

# 查询订单
order = service.get_order_by_id(order_id)
print(f"订单状态: {order.status}")
```

### 2. 直接使用DAO

```python
from custom_api.database import get_pending_order_dao, get_db_session
from custom_api.models.pending_order import OrderStatus

dao = get_pending_order_dao()

# 创建订单
order_data = {
    'order_id': 'ORDER001',
    'symbol': 'EURUSD',
    'side': 'B',
    'price': 1.1000,
    'quantity': 100000
}

order = dao.create(order_data)

# 查询订单
order = dao.get_by_order_id('ORDER001')

# 更新状态
dao.update_order_status('ORDER001', OrderStatus.FILLED)
```

### 3. 使用事务

```python
from custom_api.database import get_db_transaction

with get_db_transaction() as session:
    # 在事务中执行多个操作
    order1 = dao.create(order_data1, session)
    order2 = dao.create(order_data2, session)
    # 事务会自动提交或回滚
```

## 配置管理

### 环境变量配置

```bash
# 数据库类型
export DB_TYPE=mysql

# MySQL配置
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USERNAME=trading_user
export MYSQL_PASSWORD=secure_password
export MYSQL_DATABASE=trading_system

# 连接池配置
export DB_POOL_SIZE=20
export DB_MAX_OVERFLOW=30

# 调试配置
export DB_ECHO=false
```

### 代码配置

```python
import os
from custom_api.database.config import get_database_config

# 设置环境变量
os.environ['DB_TYPE'] = 'sqlite'
os.environ['SQLITE_PATH'] = 'data/my_trading.db'

# 重新加载配置
config = get_database_config()
config_manager.reload_config()
```

## DAO操作详解

### 基础CRUD操作

```python
dao = get_pending_order_dao()

# 创建
order = dao.create(order_data)

# 读取
order = dao.get(order_id)
order = dao.get_by_field('order_id', 'ORDER001')

# 更新
dao.update(order, {'status': OrderStatus.FILLED})

# 删除
dao.delete(order_id)

# 统计
count = dao.count()
exists = dao.exists(order_id)
```

### 批量操作

```python
# 批量创建
orders_data = [order_data1, order_data2, order_data3]
created_orders = dao.batch_create(orders_data)

# 批量更新
updates = [
    {'id': 1, 'status': OrderStatus.FILLED},
    {'id': 2, 'status': OrderStatus.CANCELLED}
]
updated_count = dao.batch_update(updates)
```

### 复杂查询

```python
# 条件过滤
orders = dao.filter_by(
    filters={'symbol': 'EURUSD', 'status': OrderStatus.PENDING},
    skip=0,
    limit=20,
    order_by='create_time',
    desc_order=True
)

# 按合约查询
orders = dao.get_orders_by_symbol('EURUSD', OrderStatus.PENDING)

# 按时间范围查询
from datetime import datetime, timedelta
start_time = datetime.now() - timedelta(days=7)
end_time = datetime.now()
orders = dao.get_orders_by_time_range(start_time, end_time)

# 获取统计信息
stats = dao.get_statistics(start_time, end_time)
```

## 服务层功能

### 订单管理

```python
service = get_order_service()

# 创建订单（集成to_order）
order_id = service.create_order_from_to_order_params(**to_order_params)

# 分页查询
result = service.get_orders_by_symbol('EURUSD', page=1, page_size=20)
orders = result['orders']
pagination = result['pagination']

# 获取待处理订单
pending_result = service.get_pending_orders(page=1, page_size=10)

# 更新订单状态
service.update_order_status(order_id, OrderStatus.FILLED, filled_quantity=100000)

# 取消订单
service.cancel_order(order_id, reason="用户取消")
```

### 统计和分析

```python
# 获取统计信息
stats = service.get_order_statistics(days=30)
print(f"总订单数: {stats['total_orders']}")
print(f"状态分布: {stats['status_distribution']}")

# 获取最近订单
recent_orders = service.get_recent_orders(hours=24, limit=50)

# 批量更新
updates = [
    {'order_id': 'ORDER001', 'status': OrderStatus.FILLED},
    {'order_id': 'ORDER002', 'status': OrderStatus.CANCELLED}
]
updated_count = service.batch_update_orders(updates)
```

## 数据库支持

### SQLite（默认）
```python
# 配置
DB_TYPE=sqlite
SQLITE_PATH=data/trading_system.db

# 特点
- 无需额外安装
- 适合开发和测试
- 支持WAL模式
- 自动创建数据目录
```

### MySQL
```python
# 配置
DB_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=trading_user
MYSQL_PASSWORD=secure_password
MYSQL_DATABASE=trading_system

# 依赖
pip install pymysql
```

### PostgreSQL
```python
# 配置
DB_TYPE=postgresql
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=password
POSTGRES_DATABASE=trading_system

# 依赖
pip install psycopg2-binary
```

## 最佳实践

### 1. 连接管理
```python
# 使用上下文管理器
with get_db_session() as session:
    # 执行数据库操作
    pass

# 使用事务
with get_db_transaction() as session:
    # 执行事务操作
    pass
```

### 2. 错误处理
```python
try:
    order = dao.create(order_data)
except SQLAlchemyError as e:
    logger.error(f"数据库操作失败: {e}")
    # 处理错误
```

### 3. 性能优化
```python
# 使用批量操作
dao.batch_create(orders_data)

# 使用索引字段查询
dao.get_orders_by_symbol('EURUSD')  # 使用索引

# 合理设置分页
dao.get_multi(skip=0, limit=100)  # 避免大量数据
```

### 4. 资源清理
```python
# 应用关闭时清理资源
from custom_api.database import cleanup_database

def shutdown_handler():
    cleanup_database()
```

## 扩展开发

### 创建新的DAO

```python
from custom_api.dao.base_dao import BaseDAO
from your_model import YourModel


class YourModelDAO(BaseDAO[YourModel, dict, dict]):
    def __init__(self):
        super().__init__(YourModel)

    def custom_query(self, param):
        # 实现自定义查询
        pass
```

### 创建新的服务

```python
from custom_api.database import get_db_session

class YourService:
    def __init__(self):
        self.dao = YourModelDAO()
    
    def business_logic(self, data):
        with get_db_session() as session:
            # 实现业务逻辑
            pass
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库配置
   - 确认数据库服务运行
   - 验证网络连接

2. **导入错误**
   - 检查模块路径
   - 确认依赖安装
   - 验证Python路径

3. **性能问题**
   - 检查索引使用
   - 优化查询条件
   - 调整连接池大小

### 调试技巧

```python
# 启用SQL日志
os.environ['DB_ECHO'] = 'true'

# 启用连接池日志
os.environ['DB_ECHO_POOL'] = 'true'

# 检查连接状态
from custom_api.database import get_db_manager
manager = get_db_manager()
success = manager.test_connection()
```

这个数据库系统提供了完整的数据访问解决方案，支持从简单的CRUD操作到复杂的业务逻辑，具有良好的扩展性和维护性。
