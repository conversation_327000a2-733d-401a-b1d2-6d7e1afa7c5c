# PendingOrder 模型使用指南

## 概述

`PendingOrder` 是一个完整的 SQLAlchemy 模型，用于存储和管理交易系统中的待处理订单。该模型与 `to_order` 函数参数完全对应，提供了完整的订单生命周期管理功能。

## 主要特性

### ✅ 完整的枚举定义
- **OrderType**: 订单类型（点击单、限价单、SOR单等）
- **OrderSide**: 交易方向（买入/卖出）
- **EffectType**: 开平仓类型（开仓、平仓、平今、平昨）
- **TimeInForce**: 时效性（GTC、FOK、FAK、GFD、GTD）
- **MarketType**: 执行市场（内部、外部、内外部）
- **OrderStatus**: 订单状态（挂单中、部分成交、完全成交等）

### ✅ 优化的数据库设计
- **索引优化**: 为常用查询字段创建了复合索引
- **约束完整**: 包含数据完整性检查约束
- **审计字段**: 创建时间、更新时间、版本控制等
- **扩展性**: 支持未来功能扩展

### ✅ 便捷的工厂方法
- `from_to_order_params()`: 直接从 `to_order` 函数参数创建订单
- `to_dict()`: 转换为字典格式，便于序列化

## 基本使用

### 1. 创建基础订单

```python
from custom_api.models.pending_order import PendingOrder, OrderSide, OrderType, EffectType

# 创建限价买单
order = PendingOrder(
    order_id="ORDER001",
    symbol="EURUSD",
    side=OrderSide.BUY,
    price=1.1000,
    quantity=100000,
    effect=EffectType.OPEN,
    order_type=OrderType.LIMIT_ORDER,
    channel_code="CHANNEL001"
)

session.add(order)
session.commit()
```

### 2. 从 to_order 参数创建订单

```python
# to_order 函数参数
to_order_params = {
    'symbol': 'XAUUSD',
    'side': 'S',
    'price': 2000.0,
    'quantity': 1,
    'effect': 1,
    'order_type': 2,
    'stop_loss_price': 2010.0,
    'take_profit_price': 1990.0
}

# 使用工厂方法创建订单
order = PendingOrder.from_to_order_params(**to_order_params)
session.add(order)
session.commit()
```

### 3. 复杂订单类型

```python
# SOR 智能订单路由
sor_order = PendingOrder(
    order_id="SOR001",
    symbol="EURUSD",
    side=OrderSide.BUY,
    price=1.1000,
    quantity=500000,
    effect=EffectType.OPEN,
    order_type=OrderType.SOR_ORDER,
    in_out_market=MarketType.BOTH,
    bp=5  # 容忍5bp滑点
)

# 债券订单
bond_order = PendingOrder(
    order_id="BOND001",
    symbol="BOND001",
    side=OrderSide.BUY,
    price=100.50,
    quantity=1000000,
    effect=EffectType.OPEN,
    order_type=OrderType.LIMIT_ORDER,
    bond_quote_type=BondQuoteType.CONTINUOUS,
    maturity_date="20251225",
    bp=10
)

# 掉期订单
swap_order = PendingOrder(
    order_id="SWAP001",
    symbol="EURUSD",
    side=OrderSide.BUY,
    price=1.1000,
    quantity=100000,
    effect=EffectType.OPEN,
    order_type=OrderType.LIMIT_ORDER,
    value_date="20241225",
    maturity_date="20250125"
)
```

## 查询示例

### 1. 基础查询

```python
# 按合约查询
eurusd_orders = session.query(PendingOrder).filter_by(symbol="EURUSD").all()

# 按状态查询
pending_orders = session.query(PendingOrder).filter_by(status=OrderStatus.PENDING).all()

# 按渠道查询
channel_orders = session.query(PendingOrder).filter_by(channel_code="CHANNEL001").all()
```

### 2. 复合查询（利用索引优化）

```python
# 合约+状态查询（使用复合索引）
eurusd_pending = session.query(PendingOrder).filter(
    PendingOrder.symbol == "EURUSD",
    PendingOrder.status == OrderStatus.PENDING
).all()

# 渠道+状态查询（使用复合索引）
channel_pending = session.query(PendingOrder).filter(
    PendingOrder.channel_code == "CHANNEL001",
    PendingOrder.status == OrderStatus.PENDING
).all()

# 方向+状态查询（使用复合索引）
buy_pending = session.query(PendingOrder).filter(
    PendingOrder.side == OrderSide.BUY,
    PendingOrder.status == OrderStatus.PENDING
).all()
```

### 3. 时间范围查询

```python
from datetime import datetime, timedelta

# 查询最近24小时的订单
yesterday = datetime.now() - timedelta(days=1)
recent_orders = session.query(PendingOrder).filter(
    PendingOrder.create_time >= yesterday
).all()

# 查询指定时间范围的订单
start_time = datetime(2024, 8, 1)
end_time = datetime(2024, 8, 31)
monthly_orders = session.query(PendingOrder).filter(
    PendingOrder.create_time.between(start_time, end_time)
).all()
```

## 订单状态管理

```python
# 更新订单状态
order = session.query(PendingOrder).filter_by(order_id="ORDER001").first()
if order:
    order.status = OrderStatus.FILLED
    order.filled_quantity = order.quantity
    order.fill_time = datetime.now()
    order.updated_by = "system"
    order.version += 1
    session.commit()

# 部分成交
order.status = OrderStatus.PARTIAL_FILLED
order.filled_quantity = order.quantity * 0.5
session.commit()

# 取消订单
order.status = OrderStatus.CANCELLED
order.error_message = "用户取消"
session.commit()
```

## 数据导出

```python
# 转换为字典格式
order_dict = order.to_dict()
print(order_dict)

# 批量导出
orders = session.query(PendingOrder).filter_by(symbol="EURUSD").all()
orders_data = [order.to_dict() for order in orders]

# 转换为JSON
import json
json_data = json.dumps(orders_data, indent=2)
```

## 数据库表结构

### 主要字段

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| `id` | Integer | 主键ID | 自增 |
| `order_id` | String(64) | 订单唯一ID | 唯一，非空 |
| `symbol` | String(32) | 合约代码 | 非空，索引 |
| `side` | Enum | 交易方向 | 非空 |
| `price` | Float | 价格 | 可空，正数 |
| `quantity` | Float | 数量 | 可空，正数 |
| `status` | Enum | 订单状态 | 非空，索引 |

### 索引设计

- `ix_pending_orders_order_id`: 订单ID唯一索引
- `idx_symbol_status`: 合约+状态复合索引
- `idx_channel_status`: 渠道+状态复合索引
- `idx_side_status`: 方向+状态复合索引
- `idx_create_time`: 创建时间索引

### 约束检查

- `check_positive_quantity`: 数量必须为正数
- `check_non_negative_filled`: 成交量不能为负
- `check_filled_not_exceed_quantity`: 成交量不能超过订单量
- `check_positive_price`: 价格必须为正数或空
- `check_positive_version`: 版本号必须为正数

## 最佳实践

1. **使用枚举**: 始终使用定义的枚举类型，避免硬编码字符串
2. **利用索引**: 查询时优先使用已建立索引的字段组合
3. **版本控制**: 更新订单时递增版本号，便于并发控制
4. **审计追踪**: 记录创建者和更新者信息
5. **状态管理**: 合理设置订单状态转换逻辑
6. **错误处理**: 利用数据库约束进行数据完整性检查

## 与 to_order 函数集成

该模型设计完全兼容 `to_order` 函数，可以无缝集成到交易系统中：

```python
def enhanced_to_order(**kwargs):
    """增强版 to_order 函数，支持数据库持久化"""
    
    # 创建订单记录
    order = PendingOrder.from_to_order_params(**kwargs)
    session.add(order)
    session.commit()
    
    try:
        # 调用原始 to_order 函数
        external_order_id = to_order(**kwargs)
        
        if external_order_id:
            # 更新外部订单ID
            order.external_order_id = external_order_id
            order.status = OrderStatus.PENDING
            order.submit_time = datetime.now()
        else:
            # 订单失败
            order.status = OrderStatus.REJECTED
            order.error_message = "订单提交失败"
        
        session.commit()
        return order.order_id
        
    except Exception as e:
        order.status = OrderStatus.REJECTED
        order.error_message = str(e)
        session.commit()
        return None
```

这样就实现了完整的订单生命周期管理，从创建到执行再到状态跟踪的全流程数据持久化。
