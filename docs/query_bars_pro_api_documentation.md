# query_bars_pro 函数设计文档

## 1. 函数概述

`query_bars_pro` 函数是量化交易系统中的核心行情数据获取API，用于获取指定时间段内的K线（Bar）数据。该函数支持外汇、固收等多种金融产品的历史行情查询，提供了灵活的时间范围控制、多种数据格式输出和字段筛选功能，是技术分析和策略回测的重要工具。

### 主要功能
- 支持多种时间周期的K线数据获取（1分钟、5分钟、1小时、1日等）
- 提供灵活的时间范围查询（开始时间、结束时间、数量限制）
- 支持多种数据格式输出（pandas、numpy、dict）
- 提供字段筛选功能，可指定返回特定字段
- 兼容多种行情数据源
- 支持多种时间格式输入

## 2. 技术规格

### 函数签名

```python
def query_bars_pro(
    symbol, type_, source, 
    count=-1, fields=None, data_type=1, 
    start_datetime='', end_datetime=''
) -> Union[pd.DataFrame, np.ndarray, List[Dict], None]
```

### 参数分类

#### 必选参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `symbol` | str | 合约唯一代码，如'EURUSD'、'XAUUSD'等 |
| `type_` | str | 数据类型，如'1D_BAR_DEPTH'、'1H_BAR_DEPTH'等 |
| `source` | str | 行情来源，如'MT4'、'UBS_HO'、'CFETS_LC'等 |

#### 可选参数
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `count` | int | -1 | bar数量，-1表示全部返回，正数表示最多返回的条数 |
| `fields` | list | None | 指定返回字段，None表示返回所有字段 |
| `data_type` | int | 1 | 数据返回类型：0-pandas，1-numpy，2-dict |
| `start_datetime` | str | '' | bar开始时间，空字符串表示不限制 |
| `end_datetime` | str | '' | bar结束时间，空字符串表示当前时间 |

### 返回值
- **pandas DataFrame** (data_type=0): 包含datetime列的结构化数据
- **numpy ndarray** (data_type=1): 数组格式的数据
- **dict list** (data_type=2): 字典列表格式的数据
- **None**: 查询失败或无数据时返回

### 返回数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `time` | int | 时间戳（毫秒级） |
| `open` | float | 开盘价 |
| `close` | float | 收盘价 |
| `high` | float | 最高价 |
| `low` | float | 最低价 |
| `datetime` | datetime | 时间对象（仅pandas格式） |

## 3. 业务场景

### 适用市场
- **外汇市场**: 主要货币对的历史K线数据
- **贵金属市场**: 黄金、白银等贵金属的价格走势
- **固收市场**: 债券等固收产品的历史行情
- **其他金融产品**: 支持多种金融工具的历史数据

### 应用场景
- **技术分析**: 获取历史价格数据进行技术指标计算
- **策略回测**: 提供历史数据用于交易策略的回测验证
- **风险管理**: 分析历史波动率和价格分布
- **报表生成**: 生成历史价格走势图表和统计报告
- **实时监控**: 获取最新的K线数据用于实时分析

## 4. 参数详解

### 时间周期类型 (type_)
```python
# 支持的时间周期类型
type_options = {
    '1M_BAR_DEPTH': '1分钟K线',
    '5M_BAR_DEPTH': '5分钟K线', 
    '15M_BAR_DEPTH': '15分钟K线',
    '30M_BAR_DEPTH': '30分钟K线',
    '1H_BAR_DEPTH': '1小时K线',
    '4H_BAR_DEPTH': '4小时K线',
    '1D_BAR_DEPTH': '日K线',
    '1W_BAR_DEPTH': '周K线',
    '1MO_BAR_DEPTH': '月K线'
}
```

### 时间格式支持
```python
# 支持的时间格式
time_formats = [
    '%Y-%m-%d %H:%M',      # 2021-12-10 09:30
    '%Y-%m-%d %H:%M:%S',   # 2021-12-10 09:30:00
    '%Y%m%d%H%M%S'         # 20211210093000
]
```

### 数据源类型 (source)
```python
# 常用数据源
data_sources = {
    'MT4': 'MetaTrader 4平台数据',
    'UBS_HO': 'UBS银行数据源',
    'CFETS_LC': '中国外汇交易中心数据',
    'REUTERS': '路透社数据源',
    'BLOOMBERG': '彭博数据源'
}
```

### 字段筛选 (fields)
```python
# 可选择的字段
available_fields = ['time', 'open', 'close', 'high', 'low']

# 字段筛选示例
fields_examples = {
    None: '返回所有字段',
    ['open', 'close']: '只返回开盘价和收盘价',
    ['high', 'low']: '只返回最高价和最低价',
    ['time', 'close']: '只返回时间和收盘价'
}
```

## 5. 使用示例

### 基础查询示例
```python
# 获取欧元美元日K线数据（最近100根）
bars = query_bars_pro(
    symbol='EURUSD',
    type_='1D_BAR_DEPTH',
    source='MT4',
    count=100
)
```

### 指定时间范围查询
```python
# 获取指定时间段的小时K线数据
bars = query_bars_pro(
    symbol='XAUUSD',
    type_='1H_BAR_DEPTH',
    source='UBS_HO',
    start_datetime='2021-12-10 00:00',
    end_datetime='2021-12-20 23:59',
    data_type=0  # 返回pandas DataFrame
)
```

### 字段筛选查询
```python
# 只获取开盘价和收盘价
bars = query_bars_pro(
    symbol='GBPUSD',
    type_='5M_BAR_DEPTH',
    source='MT4',
    count=200,
    fields=['open', 'close'],
    data_type=2  # 返回dict格式
)
```

### 技术分析应用示例
```python
import pandas as pd
import numpy as np

# 获取数据用于技术分析
def get_sma_analysis(symbol, period=20):
    """计算简单移动平均线"""
    bars = query_bars_pro(
        symbol=symbol,
        type_='1D_BAR_DEPTH',
        source='MT4',
        count=period * 2,
        data_type=0  # pandas格式
    )
    
    if bars is not None and not bars.empty:
        # 计算简单移动平均线
        bars['SMA'] = bars['close'].rolling(window=period).mean()
        return bars
    return None

# 使用示例
sma_data = get_sma_analysis('EURUSD', 20)
```

### 批量数据获取示例
```python
def get_multiple_symbols_data(symbols, type_='1D_BAR_DEPTH', count=100):
    """批量获取多个品种的K线数据"""
    results = {}
    
    for symbol in symbols:
        try:
            bars = query_bars_pro(
                symbol=symbol,
                type_=type_,
                source='MT4',
                count=count,
                data_type=0
            )
            if bars is not None:
                results[symbol] = bars
        except Exception as e:
            print(f"获取{symbol}数据失败: {e}")
    
    return results

# 使用示例
symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']
multi_data = get_multiple_symbols_data(symbols)
```

## 6. 错误处理

### 常见错误情况
1. **网络连接失败**: 无法连接到数据源服务器
2. **参数格式错误**: 时间格式不正确或参数类型错误
3. **数据源无响应**: 指定的数据源暂时不可用
4. **合约代码错误**: 指定的symbol不存在或格式错误
5. **时间范围无效**: 开始时间晚于结束时间
6. **数据为空**: 指定时间范围内无数据

### 错误处理最佳实践
```python
def safe_query_bars(symbol, type_, source, **kwargs):
    """安全的K线数据查询函数"""
    try:
        # 参数验证
        if not symbol or not isinstance(symbol, str):
            raise ValueError("symbol参数必须是非空字符串")
        
        if not type_ or not isinstance(type_, str):
            raise ValueError("type_参数必须是非空字符串")
            
        # 执行查询
        bars = query_bars_pro(
            symbol=symbol,
            type_=type_,
            source=source,
            **kwargs
        )
        
        # 结果验证
        if bars is None:
            print(f"未获取到{symbol}的数据，请检查参数或网络连接")
            return None
            
        # 数据格式检查
        if kwargs.get('data_type', 1) == 0:  # pandas格式
            if bars.empty:
                print(f"{symbol}在指定时间范围内无数据")
                return None
        elif isinstance(bars, (list, np.ndarray)) and len(bars) == 0:
            print(f"{symbol}在指定时间范围内无数据")
            return None
            
        print(f"成功获取{symbol}的{len(bars)}条K线数据")
        return bars
        
    except ValueError as e:
        print(f"参数错误: {e}")
        return None
    except Exception as e:
        print(f"查询异常: {e}")
        return None
```

### 重试机制示例
```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    result = func(*args, **kwargs)
                    if result is not None:
                        return result
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"第{attempt + 1}次尝试失败，{delay}秒后重试...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3, delay=2)
def robust_query_bars(symbol, type_, source, **kwargs):
    """带重试机制的K线查询"""
    return query_bars_pro(symbol, type_, source, **kwargs)
```

## 7. 注意事项和限制

### 重要注意事项
1. **时间格式**: 确保时间字符串格式正确，支持三种格式
2. **数据源可用性**: 不同数据源的可用时间和数据质量可能不同
3. **网络依赖**: 函数依赖网络连接，需要确保网络畅通
4. **数据延迟**: 实时数据可能有延迟，具体延迟取决于数据源
5. **字段限制**: fields参数只能筛选预定义的字段
6. **内存使用**: 大量数据查询时注意内存使用情况

### 性能优化建议
- **合理设置count**: 避免一次性获取过多数据
- **使用字段筛选**: 只获取需要的字段以减少数据传输
- **缓存机制**: 对于重复查询的数据实现本地缓存
- **批量处理**: 避免频繁的小批量查询
- **异步处理**: 对于大量数据查询考虑使用异步方式

### 数据质量注意事项
- **数据完整性**: 检查返回数据是否完整，是否有缺失
- **异常值处理**: 注意识别和处理价格异常值
- **时区问题**: 注意时间戳的时区设置
- **节假日数据**: 某些市场在节假日可能无数据

## 8. 相关API

### 行情数据相关函数
- `query_bars()`: 基础K线数据查询函数
- `get_price()`: 获取实时价格数据
- `put_maker_symbol()`: 做市商品种设置

### 典型数据分析工作流程
```python
# 1. 获取历史数据
bars = query_bars_pro('EURUSD', '1D_BAR_DEPTH', 'MT4', count=100, data_type=0)

# 2. 数据预处理
if bars is not None and not bars.empty:
    # 计算技术指标
    bars['MA5'] = bars['close'].rolling(5).mean()
    bars['MA20'] = bars['close'].rolling(20).mean()
    
    # 3. 策略信号生成
    bars['signal'] = np.where(bars['MA5'] > bars['MA20'], 1, -1)
    
    # 4. 结果分析
    print(f"数据范围: {bars['datetime'].min()} 到 {bars['datetime'].max()}")
    print(f"价格范围: {bars['close'].min():.5f} - {bars['close'].max():.5f}")
```

### 与其他API的配合使用
```python
# 结合订单API使用
def trading_strategy_example():
    # 获取最新K线数据
    recent_bars = query_bars_pro(
        symbol='EURUSD',
        type_='1H_BAR_DEPTH', 
        source='MT4',
        count=50,
        data_type=0
    )
    
    if recent_bars is not None and not recent_bars.empty:
        # 计算信号
        current_price = recent_bars['close'].iloc[-1]
        ma_20 = recent_bars['close'].rolling(20).mean().iloc[-1]
        
        # 根据信号下单（需要配合to_order函数）
        if current_price > ma_20:
            print("生成买入信号")
            # order_id = to_order('EURUSD', 'B', current_price, 100000, effect=1)
        else:
            print("生成卖出信号")
            # order_id = to_order('EURUSD', 'S', current_price, 100000, effect=1)
```

---

**文档版本**: v1.0  
**最后更新**: 2024-08-11  
**适用版本**: quantapi 1.3.0+  
**依赖库**: pandas, numpy, requests
