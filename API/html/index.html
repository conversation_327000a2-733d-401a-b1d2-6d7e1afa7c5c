<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>QuantApi &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="快速开始" href="quick_start.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="#" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">QuantApi</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="quantapi">
<h1>QuantApi<a class="headerlink" href="#quantapi" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">索引</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">模块索引</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">搜索页面</span></a></p></li>
</ul>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="base.html#base.get_bond_cash_flow"><code class="docutils literal notranslate"><span class="pre">get_bond_cash_flow()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="base.html#base.get_bond_info"><code class="docutils literal notranslate"><span class="pre">get_bond_info()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="base.html#base.get_bull_bear_flag"><code class="docutils literal notranslate"><span class="pre">get_bull_bear_flag()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="base.html#base.get_contract"><code class="docutils literal notranslate"><span class="pre">get_contract()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="base.html#base.query_coupon_pool_bond_info"><code class="docutils literal notranslate"><span class="pre">query_coupon_pool_bond_info()</span></code></a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a><ul>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.init"><code class="docutils literal notranslate"><span class="pre">init()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onBusinessDate"><code class="docutils literal notranslate"><span class="pre">onBusinessDate()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onData"><code class="docutils literal notranslate"><span class="pre">onData()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onOrder"><code class="docutils literal notranslate"><span class="pre">onOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onQuote"><code class="docutils literal notranslate"><span class="pre">onQuote()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onQuoteOrder"><code class="docutils literal notranslate"><span class="pre">onQuoteOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onRfqQuote"><code class="docutils literal notranslate"><span class="pre">onRfqQuote()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onRfqQuoteOrder"><code class="docutils literal notranslate"><span class="pre">onRfqQuoteOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onRfqReq"><code class="docutils literal notranslate"><span class="pre">onRfqReq()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onTime"><code class="docutils literal notranslate"><span class="pre">onTime()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="event.html#event.onTrade"><code class="docutils literal notranslate"><span class="pre">onTrade()</span></code></a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a><ul>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.CCI"><code class="docutils literal notranslate"><span class="pre">CCI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.KELCHAN"><code class="docutils literal notranslate"><span class="pre">KELCHAN()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.MA"><code class="docutils literal notranslate"><span class="pre">MA()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.MACD"><code class="docutils literal notranslate"><span class="pre">MACD()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.MFI"><code class="docutils literal notranslate"><span class="pre">MFI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.MOM"><code class="docutils literal notranslate"><span class="pre">MOM()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.OBV"><code class="docutils literal notranslate"><span class="pre">OBV()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.RSI"><code class="docutils literal notranslate"><span class="pre">RSI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="talib_erayt.html#talib_erayt.STOCH"><code class="docutils literal notranslate"><span class="pre">STOCH()</span></code></a></li>
</ul>
</li>
</ul>
</div>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="quick_start.html" class="btn btn-neutral float-right" title="快速开始" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>