<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>常用枚举 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="武器库" href="arsenals_method.html" />
    <link rel="prev" title="基础对象数据" href="object.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="object.html">基础对象数据</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">常用枚举</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.ContractStatusEnum"><code class="docutils literal notranslate"><span class="pre">ContractStatusEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.ContractTypeEnum"><code class="docutils literal notranslate"><span class="pre">ContractTypeEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.EffectEnum"><code class="docutils literal notranslate"><span class="pre">EffectEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.HedgeFlagEnum"><code class="docutils literal notranslate"><span class="pre">HedgeFlagEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.InOutMarketEnum"><code class="docutils literal notranslate"><span class="pre">InOutMarketEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.OrderStatusEnum"><code class="docutils literal notranslate"><span class="pre">OrderStatusEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.OrderTypeEnum"><code class="docutils literal notranslate"><span class="pre">OrderTypeEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.PosSideEnum"><code class="docutils literal notranslate"><span class="pre">PosSideEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.PosTypeEnum"><code class="docutils literal notranslate"><span class="pre">PosTypeEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.QuoteStatusEnum"><code class="docutils literal notranslate"><span class="pre">QuoteStatusEnum</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enum_erayt.SideEnum"><code class="docutils literal notranslate"><span class="pre">SideEnum</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="enum_stu.html">常用枚举和结构</a></li>
      <li class="breadcrumb-item active">常用枚举</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/enum_erayt.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-enum_erayt">
<span id="id1"></span><h1>常用枚举<a class="headerlink" href="#module-enum_erayt" title="Link to this heading"></a></h1>
<p>所有枚举值</p>
<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.ContractStatusEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">ContractStatusEnum</span></span><a class="headerlink" href="#enum_erayt.ContractStatusEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>合约状态</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>合约状态</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>未生效</p></td>
<td><p>N</p></td>
</tr>
<tr class="row-odd"><td><p>已生效</p></td>
<td><p>V</p></td>
</tr>
<tr class="row-even"><td><p>已失效</p></td>
<td><p>I</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.ContractTypeEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">ContractTypeEnum</span></span><a class="headerlink" href="#enum_erayt.ContractTypeEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>合约类型</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>合约类型</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>基础合约</p></td>
<td><p>B</p></td>
</tr>
<tr class="row-odd"><td><p>基差合约</p></td>
<td><p>D</p></td>
</tr>
<tr class="row-even"><td><p>期差合约</p></td>
<td><p>T</p></td>
</tr>
<tr class="row-odd"><td><p>连续合约</p></td>
<td><p>S</p></td>
</tr>
<tr class="row-even"><td><p>月份合约</p></td>
<td><p>M</p></td>
</tr>
<tr class="row-odd"><td><p>非标准合约</p></td>
<td><p>N</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.EffectEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">EffectEnum</span></span><a class="headerlink" href="#enum_erayt.EffectEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>开平仓类型:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>开平仓类型</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>中性</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>开仓</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>平仓</p></td>
<td><p>2</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.HedgeFlagEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">HedgeFlagEnum</span></span><a class="headerlink" href="#enum_erayt.HedgeFlagEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>投机套保标识:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>投机套保标识</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>普通</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>投机</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>套保</p></td>
<td><p>3</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.InOutMarketEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">InOutMarketEnum</span></span><a class="headerlink" href="#enum_erayt.InOutMarketEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>内外部市场:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>执行市场</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>内部市场</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>外部市场</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>内/外部市场</p></td>
<td><p>3</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.OrderStatusEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">OrderStatusEnum</span></span><a class="headerlink" href="#enum_erayt.OrderStatusEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>订单状态:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>订单状态</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>初始化</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>运行中</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>订单拒绝</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-odd"><td><p>开仓成交</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>订单已超时</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-odd"><td><p>订单撤销中</p></td>
<td><p>6</p></td>
</tr>
<tr class="row-even"><td><p>交易已撤销</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-odd"><td><p>已结束</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>已提交</p></td>
<td><p>9</p></td>
</tr>
<tr class="row-odd"><td><p>未明</p></td>
<td><p>99</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.OrderTypeEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">OrderTypeEnum</span></span><a class="headerlink" href="#enum_erayt.OrderTypeEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>订单类型:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head" colspan="2"><dl>
<dt>订单类型       |        枚举值</dt><dd><div class="line-block">
<div class="line"><br /></div>
</div>
</dd>
</dl>
</th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td colspan="2"><p>限价单        |          2</p></td>
</tr>
<tr class="row-odd"><td><p>SOR单</p></td>
<td><p>22</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p>止损限价单      |          52</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p>止损单        |          54</p></td>
</tr>
<tr class="row-even"><td><p>OCO</p></td>
<td><p>17</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p>点击单        |         0</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p>外部限价单      |          28</p></td>
</tr>
<tr class="row-odd"><td><p>IF_DONE</p></td>
<td><p>18</p></td>
</tr>
<tr class="row-even"><td><p>IF_DONE_OCO</p></td>
<td><p>19</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.PosSideEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">PosSideEnum</span></span><a class="headerlink" href="#enum_erayt.PosSideEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>头寸方向:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>头寸方向</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>中性</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>多方向</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>空方向</p></td>
<td><p>2</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.PosTypeEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">PosTypeEnum</span></span><a class="headerlink" href="#enum_erayt.PosTypeEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>逐笔模式:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>逐笔模式</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>不启用</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>启用</p></td>
<td><p>1</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.QuoteStatusEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">QuoteStatusEnum</span></span><a class="headerlink" href="#enum_erayt.QuoteStatusEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>报价状态:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>报价状态</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>正常</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>异常</p></td>
<td><p>2</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum_erayt.SideEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum_erayt.</span></span><span class="sig-name descname"><span class="pre">SideEnum</span></span><a class="headerlink" href="#enum_erayt.SideEnum" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="object.html#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<dl>
<dt>交易方向:</dt><dd><table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>交易方向</p></th>
<th class="head"><p>枚举值</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>买入</p></td>
<td><p>B</p></td>
</tr>
<tr class="row-odd"><td><p>卖出</p></td>
<td><p>S</p></td>
</tr>
</tbody>
</table>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="object.html" class="btn btn-neutral float-left" title="基础对象数据" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="arsenals_method.html" class="btn btn-neutral float-right" title="武器库" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>