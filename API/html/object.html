<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>基础对象数据 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="常用枚举" href="enum_erayt.html" />
    <link rel="prev" title="常用枚举和结构" href="enum_stu.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">基础对象数据</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#object.BarDataList"><code class="docutils literal notranslate"><span class="pre">BarDataList</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#object.MarketDataList"><code class="docutils literal notranslate"><span class="pre">MarketDataList</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="enum_erayt.html">常用枚举</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="enum_stu.html">常用枚举和结构</a></li>
      <li class="breadcrumb-item active">基础对象数据</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/object.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-object">
<span id="id1"></span><h1>基础对象数据<a class="headerlink" href="#module-object" title="Link to this heading"></a></h1>
<dl class="py class">
<dt class="sig sig-object py" id="object.BarDataList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">object.</span></span><span class="sig-name descname"><span class="pre">BarDataList</span></span><a class="headerlink" href="#object.BarDataList" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<blockquote>
<div><p>Bar数据列表(注意：?号用具体频率代替)</p>
</div></blockquote>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>名称</p></th>
<th class="head"><p>交易所</p></th>
<th class="head"><p>渠道代码 (source)</p></th>
<th class="head"><p>数据类型</p></th>
<th class="head"><p>频率</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>UBS深度行情</p></td>
<td><p>UBS</p></td>
<td><p>UBS_HO</p></td>
<td><p>?_BAR_DEPTH</p></td>
<td><p>1N、5N、15N、30N、1H、1D、1W、1M</p></td>
</tr>
<tr class="row-odd"><td><p>JPMC深度行情</p></td>
<td><p>JPMC</p></td>
<td><p>JPMC_HO</p></td>
<td><p>?_BAR_DEPTH</p></td>
<td><p>1N、5N、15N、30N、1H、1D、1W、1M</p></td>
</tr>
<tr class="row-even"><td><p>外汇交易中心ODM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-ODM_HO</p></td>
<td><p>?_BAR_DEPTH</p></td>
<td><p>1N、5N、15N、30N、1H、1D、1W、1M</p></td>
</tr>
<tr class="row-odd"><td><p>外汇交易中心QDM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-QDM_FULL_HO</p></td>
<td><p>?_BAR_DEPTH</p></td>
<td><p>1N、5N、15N、30N、1H、1D、1W、1M</p></td>
</tr>
<tr class="row-even"><td><p>外汇交易中心QDM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-QDM_SWEEP_HO</p></td>
<td><p>?_BAR_DEPTH</p></td>
<td><p>1N、5N、15N、30N、1H、1D、1W、1M</p></td>
</tr>
</tbody>
</table>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="object.MarketDataList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">object.</span></span><span class="sig-name descname"><span class="pre">MarketDataList</span></span><a class="headerlink" href="#object.MarketDataList" title="Link to this definition"></a></dt>
<dd><p>基类：<a class="reference internal" href="#module-object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>名称</p></th>
<th class="head"><p>交易所</p></th>
<th class="head"><p>渠道代码 (source)</p></th>
<th class="head"><p>结构</p></th>
<th class="head"><p>数据类型</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>UBS深度行情</p></td>
<td><p>UBS</p></td>
<td><p>UBS_HO</p></td>
<td><p>DepthQuote</p></td>
<td><p>FXSPOT</p></td>
</tr>
<tr class="row-odd"><td><p>JPMC深度行情</p></td>
<td><p>JPMC</p></td>
<td><p>JPMC_HO</p></td>
<td><p>DepthQuote</p></td>
<td><p>FXSPOT</p></td>
</tr>
<tr class="row-even"><td><p>外汇交易中心ODM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-ODM_HO</p></td>
<td><p>DepthQuote</p></td>
<td><p>FXSPOT</p></td>
</tr>
<tr class="row-odd"><td><p>外汇交易中心QDM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-QDM_FULL_HO</p></td>
<td><p>DepthQuote</p></td>
<td><p>FXSPOT</p></td>
</tr>
<tr class="row-even"><td><p>外汇交易中心QDM</p></td>
<td><p>CFETS</p></td>
<td><p>CFETS-QDM_SWEEP_HO</p></td>
<td><p>DepthQuote</p></td>
<td><p>FXSPOT</p></td>
</tr>
</tbody>
</table>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="enum_stu.html" class="btn btn-neutral float-left" title="常用枚举和结构" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="enum_erayt.html" class="btn btn-neutral float-right" title="常用枚举" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>