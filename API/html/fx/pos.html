<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>持仓API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="做市API" href="maker.html" />
    <link rel="prev" title="行情API" href="md.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../bond/index.html">固收API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">外汇API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3"><a class="reference internal" href="md.html">行情API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">持仓API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_fx_exposure"><code class="docutils literal notranslate"><span class="pre">get_fx_exposure()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_fxpm_exposure"><code class="docutils literal notranslate"><span class="pre">get_fxpm_exposure()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_ord_position"><code class="docutils literal notranslate"><span class="pre">get_ord_position()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_position"><code class="docutils literal notranslate"><span class="pre">get_position()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_position_onroad"><code class="docutils literal notranslate"><span class="pre">get_position_onroad()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">外汇API</a></li>
      <li class="breadcrumb-item active">持仓API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/fx/pos.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.pos">
<span id="api"></span><h1>持仓API<a class="headerlink" href="#module-.pos" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="pos.get_fx_exposure">
<span class="sig-prename descclassname"><span class="pre">外汇.pos.</span></span><span class="sig-name descname"><span class="pre">get_fx_exposure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pair</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_fx_exposure" title="Link to this definition"></a></dt>
<dd><p>外汇掉期敞口查询,无数据返回空字典</p>
<dl>
<dt>参数:</dt><dd><p>pair(str): 货币对</p>
</dd>
<dt>返回:</dt><dd><dl>
<dt>{</dt><dd><p>USDJPY:{</p>
<blockquote>
<div><p>total:{amt:100, fv:1, npv:0.9,dv01:0.8}</p>
<p>tenor:{</p>
<blockquote>
<div><p>SPOT:{</p>
<blockquote>
<div><p>total:{amt:100, fv:1, npv:0.9,date:20240301,dv01:0.8}</p>
<p>record:[{amt:100, fv:1, npv:0.9,date:20240301,dv01:0.8},</p>
<blockquote>
<div><p>{amt:100, fv:1, npv:0.9,date:20240302,dv01:0.8},</p>
<p>…</p>
</div></blockquote>
<p>]</p>
</div></blockquote>
<p>},</p>
<p>1M:{…}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<p>},</p>
<p>USDCNY:{…}</p>
</dd>
</dl>
<p>}</p>
<p>货币对下total是对整个货币对的汇总</p>
<p>货币对下tenor是关键字,其下包含各个期限</p>
<blockquote>
<div><p>balance:T</p>
<p>TOM:T+1</p>
<p>SPOT:T+2</p>
<p>1W,2W,3W…远期的标准期限</p>
</div></blockquote>
<p>期限下total是期限的汇总</p>
<p>期限下record是期限的明细,明细维度是日期,同日的明细只有一条</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_fx_exposure</span><span class="p">(</span><span class="n">pair</span><span class="o">=</span><span class="s1">&#39;USDCNY&#39;</span><span class="p">)</span> <span class="n">或</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_fx_exposure</span><span class="p">()</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_fxpm_exposure">
<span class="sig-prename descclassname"><span class="pre">外汇.pos.</span></span><span class="sig-name descname"><span class="pre">get_fxpm_exposure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_fxpm_exposure" title="Link to this definition"></a></dt>
<dd><p>国际贵金属持仓查询,无数据返回空字典</p>
<dl>
<dt>参数:</dt><dd><p>symbol(str): 标的（例：XAUUSD）</p>
</dd>
<dt>返回:</dt><dd><dl>
<dt>{</dt><dd><p>total:{</p>
<blockquote>
<div><p>quantity: 100</p>
<p>},</p>
</div></blockquote>
<p>un_delivered:{</p>
<blockquote>
<div><blockquote>
<div><p>“20241010”: 50,</p>
<p>“20241011”: 30</p>
</div></blockquote>
<p>},</p>
</div></blockquote>
<p>delivered: 20</p>
</dd>
</dl>
<p>}</p>
<p>total: 标的持仓的汇总（包括已交割部分与未交割部分）</p>
<p>un_delivered: 未交割持仓</p>
<blockquote>
<div><dl>
<dt>{</dt><dd><p>“交割日”: 持仓,</p>
<p>“20241011”: 30</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
<p>delivered: 已交割持仓</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_fxpm_exposure</span><span class="p">(</span><span class="n">symbol</span><span class="o">=</span><span class="s1">&#39;XAUUSD&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_ord_position">
<span class="sig-prename descclassname"><span class="pre">外汇.pos.</span></span><span class="sig-name descname"><span class="pre">get_ord_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">order_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#pos.get_ord_position" title="Link to this definition"></a></dt>
<dd><p>根据合约或者开仓订单ID获取逐笔持仓信息,如果不传获取全部未平仓持仓</p>
<dl>
<dt>可选参数:</dt><dd><p>order_id(string):开仓订单Id (default: {None})</p>
<p>symbol(string):合约代码 (default: {None})</p>
</dd>
<dt>返回:</dt><dd><p>list&lt;dict&gt;: Position信息</p>
<p>Returns:list
[{</p>
<blockquote>
<div><p>id’(int): 唯一编号</p>
<p>symbol(string): 合约代码</p>
<p>frozenQuantity(float): 冻结量</p>
<p>quantity(float): 总持仓量</p>
<p>quantityTd(float): 今日持仓量</p>
<p>posSide(int): 头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向](default: {0})</p>
<p>profit(float): 损益</p>
<p>value(float): 估值</p>
<p>costPrice(float): 敞口价格</p>
<p>unRealizedPL(float): 未交割浮动损益</p>
<p>realizedPL(int): 已交割损益</p>
<p>washAmount(float): 持仓成本</p>
<p>time(long): 头寸时间</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_ord_position</span><span class="p">(</span><span class="mi">216868121676222464</span><span class="p">)</span>
<span class="go">    [{&#39;id&#39;: 216868121676222464, &#39;symbol&#39;: &#39;EURUSDSP&#39;, &#39;frozenQuantity&#39;: 0, &#39;quantity&#39;: 0, &#39;quantityTd&#39;: 0, &#39;posSide&#39;: 0,</span>
<span class="go">    &#39;profit&#39;: -2145.0000000000637, &#39;value&#39;: 0.0, &#39;costPrice&#39;: 1.16064, &#39;unRealizedPL&#39;: 0.0,</span>
<span class="go">    &#39;realizedPL&#39;: -2145.0000000000637, &#39;washAmount&#39;: 0.0, &#39;time&#39;: 1530524400101}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_position">
<span class="sig-prename descclassname"><span class="pre">外汇.pos.</span></span><span class="sig-name descname"><span class="pre">get_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">dict</span></span></span><a class="headerlink" href="#pos.get_position" title="Link to this definition"></a></dt>
<dd><p>根据合约获取持仓信息</p>
<dl>
<dt>必选参数:</dt><dd><p>symbol(string):合约代码</p>
</dd>
<dt>可选参数:</dt><dd><p>pos_side(int):头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向,3-净值多头,4-净值空头](default: {0})</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict
{</p>
<blockquote>
<div><p>symbol(string): 合约代码</p>
<p>quantityTd(float): 今日持仓量</p>
<p>frozenQuantityTd(float): 今日冻结量</p>
<p>frozenQuantityYestd(float): 今日之前所有冻结量</p>
<p>frozenQuantity(float): 总冻结量</p>
<p>quantity(float): 总持仓量</p>
<p>posSide(int): 头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向,3-净值多头,4-净值空头](default: {0})</p>
<p>profit(float): 损益</p>
<p>value(float): 估值</p>
<p>costPrice(float): 敞口价格</p>
<p>unRealizedPL(float): 未交割浮动损益</p>
<p>realizedPL(float): 已交割损益</p>
<p>washAmount(float): 持仓成本</p>
<p>time(long): 头寸时间</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_position</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">    {&#39;symbol&#39;: &#39;EURUSDSP&#39;, &#39;quantityTd&#39;: 0, &#39;frozenQuantityTd&#39;: 0, &#39;frozenQuantityYestd&#39;: 0,</span>
<span class="go">    &#39;frozenQuantity&#39;: 0, &#39;quantity&#39;: 0, &#39;posSide&#39;: 0,</span>
<span class="go">    &#39;profit&#39;: -2145.0000000000637, &#39;value&#39;: 0.0, &#39;costPrice&#39;: 1.16064, &#39;unRealizedPL&#39;: 0.0,</span>
<span class="go">    &#39;realizedPL&#39;: -2145.0000000000637, &#39;washAmount&#39;: 0.0, &#39;time&#39;: 1530524400101}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_position_onroad">
<span class="sig-prename descclassname"><span class="pre">外汇.pos.</span></span><span class="sig-name descname"><span class="pre">get_position_onroad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">dict</span></span></span><a class="headerlink" href="#pos.get_position_onroad" title="Link to this definition"></a></dt>
<dd><p>根据合约获取持仓信息和在途单量</p>
<dl>
<dt>必选参数:</dt><dd><p>symbol(string):合约代码</p>
</dd>
<dt>可选参数:</dt><dd><p>effect(int):–&gt;EffectEnum[0-中性,1-开仓,2-平仓,3-平今,4-平昨](default: {0})</p>
<p>pos_side(int):头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向,3-净值多头,4-净值空头](default: {0})</p>
</dd>
<dt>返回:</dt><dd><p>dict: 各方向在途单数量及总持仓</p>
<p>Returns:dict
{</p>
<blockquote>
<div><p>onroad_b(float): 买方向汇总</p>
<p>onroad_s(float): 卖方向汇总</p>
<p>quantity(float): 总持仓</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_position_onroad</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">    {&quot;onroad_b&quot;,60.0,&quot;onroad_s&quot;,50.0,&quot;quantity&quot;:1000}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="md.html" class="btn btn-neutral float-left" title="行情API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="maker.html" class="btn btn-neutral float-right" title="做市API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>