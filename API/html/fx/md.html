<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>行情API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="持仓API" href="pos.html" />
    <link rel="prev" title="订单API" href="deal.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../bond/index.html">固收API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">外汇API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">行情API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#md.get_price"><code class="docutils literal notranslate"><span class="pre">get_price()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.put_maker_symbol"><code class="docutils literal notranslate"><span class="pre">put_maker_symbol()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_bars"><code class="docutils literal notranslate"><span class="pre">query_bars()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_bars_pro"><code class="docutils literal notranslate"><span class="pre">query_bars_pro()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="pos.html">持仓API</a></li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">外汇API</a></li>
      <li class="breadcrumb-item active">行情API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/fx/md.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.md">
<span id="api"></span><h1>行情API<a class="headerlink" href="#module-.md" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="md.get_price">
<span class="sig-prename descclassname"><span class="pre">外汇.md.</span></span><span class="sig-name descname"><span class="pre">get_price</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#md.get_price" title="Link to this definition"></a></dt>
<dd><p>获取最新行情</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>symbol</strong> (<em>str</em>) – 合约唯一代码</p>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>type</strong> (<em>str</em>) – 数据类型(default: {None})</p></li>
<li><p><strong>source</strong> (<em>str</em>) – 行情来源(default: {None})</p></li>
<li><p><strong>fields</strong> (<em>list</em>) – 指定返回对象字段(default: {None})</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>行情对象 list&lt;dict&gt;</p>
<p>Returns:list
[{</p>
<blockquote>
<div><p>status(str): 价格状态 –&gt;QuoteStatusEnum[1 - 正常, 2 - 异常]</p>
<p>source(str): 数据渠道</p>
<p>type(str): 数据类型</p>
<p>symbol(str): 合约代码</p>
<p>time(int): 时间戳</p>
<p>best_bid(float): 最优买价</p>
<p>best_bid_amt(float): 最优买价数量</p>
<p>best_ask(float): 最优卖价</p>
<p>best_ask_amt(float): 最优卖价数量</p>
<p>asks(list): 卖出报盘价格，asks[0] 代表盘口卖一档报盘价</p>
<p>ask_vols(list): 卖出报盘数量，ask_vols[0]代表盘口卖一档报盘数量</p>
<p>bids(list): 买入报盘价格，bids[0]代表盘口买一档报盘价</p>
<p>bid_vols(list): 买入报盘数量，bid_vols[0]代表盘口买一档报盘数量</p>
<p>limit_up(float): 涨停价</p>
<p>limit_down(float): 跌停价</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">get_price</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">)</span>
<span class="go">    [{&#39;status&#39;: &#39;1&#39;, &#39;source&#39;: &#39;CFETS_LC&#39;, &#39;type&#39;: &#39;ODM_DEPTH&#39;, &#39;symbol&#39;: &#39;EURUSDSP&#39;,</span>
<span class="go">    &#39;time&#39;: 1598922000100, &#39;best_bid&#39;: 1.19907, &#39;best_bid_amt&#39;: 94, &#39;best_ask&#39;: 1.19919,</span>
<span class="go">    &#39;best_ask_amt&#39;: 94, &#39;asks&#39;: [1.19919, 1.19925, 1.19931, 1.19937, 1.19943],</span>
<span class="go">    &#39;ask_vols&#39;: [94, 94, 94, 94, 94], &#39;bids&#39;: [1.19907, 1.19901, 1.19895, 1.19889, 1.19883],</span>
<span class="go">    &#39;bid_vols&#39;: [94, 94, 94, 94, 94], &#39;limit_up&#39;: &#39;&#39;, &#39;limit_down&#39;: &#39;&#39;}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.put_maker_symbol">
<span class="sig-prename descclassname"><span class="pre">外汇.md.</span></span><span class="sig-name descname"><span class="pre">put_maker_symbol</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">floor_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.put_maker_symbol" title="Link to this definition"></a></dt>
<dd><p>获取做市合约以及floor_code信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>floor_code</strong> (<em>floor_code  str</em>) – </p></li>
<li><p><strong>[</strong><strong>做市合约的货币对</strong> (<em>symbol_list list</em>) – </p></li>
</ul>
</dd>
</dl>
<dl class="simple">
<dt>返回:</dt><dd><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_bars">
<span class="sig-prename descclassname"><span class="pre">外汇.md.</span></span><span class="sig-name descname"><span class="pre">query_bars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.query_bars" title="Link to this definition"></a></dt>
<dd><p>获取一段时间的N根bar数据</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol  str</em>) – </p></li>
<li><p><strong>数据类型</strong> (<em>type    str</em>) – </p></li>
<li><p><strong>行情来源</strong> (<em>source  str</em>) – </p></li>
<li><p><strong>bar数量</strong> (<em>count   int</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>指定返回对象字段</strong><strong>(</strong><strong>default</strong> (<em>fields  list</em>) – {None})</p></li>
<li><p><strong>dataframe格式</strong><strong>(</strong><strong>default</strong> (<em>df      bool     是否返回</em>) – {False})</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>Bar对象    list&lt;dict&gt;</p>
<p>Returns:list
[{</p>
<blockquote>
<div><p>time(int):时间戳</p>
<p>open(float):开盘价</p>
<p>close(float):收盘价</p>
<p>high(float):最高价</p>
<p>low(float):最低价</p>
<p>strat(int):bar的开始时间</p>
<p>end(int):bar的结束时间</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">query_bars</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="n">type_</span><span class="o">=</span><span class="s2">&quot;5N_BAR_ODM_DEPTH&quot;</span><span class="p">,</span> <span class="n">source</span><span class="o">=</span><span class="s2">&quot;CFETS_LC&quot;</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;open&quot;</span><span class="p">,</span> <span class="s2">&quot;close&quot;</span><span class="p">])</span>
<span class="go">    [{&#39;time&#39;: 1532017800200, &#39;start&#39;: 1532018100000.0, &#39;end&#39;: 1532018400000.0,</span>
<span class="go">    &#39;open&#39;: 1.1648800000000001, &#39;close&#39;: 1.16487, &#39;high&#39;: 1.1648800000000001, &#39;low&#39;: 1.1648649999999998}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_bars_pro">
<span class="sig-prename descclassname"><span class="pre">外汇.md.</span></span><span class="sig-name descname"><span class="pre">query_bars_pro</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#md.query_bars_pro" title="Link to this definition"></a></dt>
<dd><p>获取一段时间获取N根bar数据</p>
<dl>
<dt>必填参数:</dt><dd><p>symbol  str     合约唯一代码</p>
<p><a href="#id1"><span class="problematic" id="id2">type_</span></a>    str     数据类型</p>
<p>source  str     行情来源</p>
</dd>
<dt>其他参数:</dt><dd><p>count   int      bar数量(default: {-1}), 表示全部返回</p>
<p>fields  list     指定返回对象字段(default: {None})</p>
<p>data_type int    数据返回类型 0 表示pandas结构 1 表示numpy结构 2 表示dict结构(default: {1})</p>
<p>start_datatime str bar的开始时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
<p>end_datatime str bar的结束时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
</dd>
<dt>返回:</dt><dd><p>Bar数据 数据类型根据入参的data_type值而变</p>
<p>time(int):时间戳</p>
<p>open(float):开盘价</p>
<p>close(float):收盘价</p>
<p>high(float):最高价</p>
<p>low(float):最低价</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">quantapi.enums</span><span class="w"> </span><span class="kn">import</span> <span class="n">DATA_TYPE_PANDAS</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">bars</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">query_bars_pro</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="s2">&quot;1D_BAR_DEPTH&quot;</span><span class="p">,</span>  <span class="s2">&quot;UBS_HO&quot;</span><span class="p">,</span> <span class="n">count</span><span class="o">=-</span><span class="mi">1</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="n">DATA_TYPE_PANDAS</span><span class="p">,</span> <span class="n">start_datatime</span><span class="o">=</span><span class="s2">&quot;2021-12-10 00:00&quot;</span><span class="p">,</span> <span class="n">end_datatime</span><span class="o">=</span><span class="s2">&quot;2021-12-20 00:00&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="deal.html" class="btn btn-neutral float-left" title="订单API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="pos.html" class="btn btn-neutral float-right" title="持仓API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>