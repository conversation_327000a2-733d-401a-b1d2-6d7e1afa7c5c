<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>订单API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="行情API" href="md.html" />
    <link rel="prev" title="外汇API" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../bond/index.html">固收API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">外汇API</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">订单API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#deal.cancel_order"><code class="docutils literal notranslate"><span class="pre">cancel_order()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#deal.get_order"><code class="docutils literal notranslate"><span class="pre">get_order()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#deal.get_orders"><code class="docutils literal notranslate"><span class="pre">get_orders()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#deal.to_order"><code class="docutils literal notranslate"><span class="pre">to_order()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="md.html">行情API</a></li>
<li class="toctree-l3"><a class="reference internal" href="pos.html">持仓API</a></li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">外汇API</a></li>
      <li class="breadcrumb-item active">订单API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/fx/deal.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.deal">
<span id="api"></span><h1>订单API<a class="headerlink" href="#module-.deal" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="deal.cancel_order">
<span class="sig-prename descclassname"><span class="pre">外汇.deal.</span></span><span class="sig-name descname"><span class="pre">cancel_order</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">orderid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">channel_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#deal.cancel_order" title="Link to this definition"></a></dt>
<dd><p>根据条件撤销委托挂单, 注意如果订单为瞬时属性的订单,则不会被撤单</p>
<dl>
<dt>参数:</dt><dd><p>orderid(str):订单ID (default: {None})
channel_code(string):市场代码 (default: {None})
symbol(string):合约名 (default: {None})
side(string):持仓方向–&gt;SideEnum{B:买入,S:卖出}  (default: {None})</p>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">deal</span><span class="o">.</span><span class="n">cancel_order</span><span class="p">()</span> <span class="c1"># 撤销全部委托</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">deal</span><span class="o">.</span><span class="n">cancel_order</span><span class="p">(</span><span class="mi">216867660628103168</span><span class="p">)</span> <span class="c1"># 撤销指定委托</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="deal.get_order">
<span class="sig-prename descclassname"><span class="pre">外汇.deal.</span></span><span class="sig-name descname"><span class="pre">get_order</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">orderid</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#deal.get_order" title="Link to this definition"></a></dt>
<dd><p>获取订单信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>orderid</strong> (<em>str</em>) – 订单ID</p>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>object: 一个order委托对象，如果不存在对应的order，返回None order结构</p>
<p>Returns:dict
{</p>
<blockquote>
<div><p>id(str):订单ID</p>
<p>channelCode(string):渠道</p>
<p>symbol(string):合约</p>
<p>order_type (int): 订单类型:0:点击单 2:限价单(默认) 15:SOR单 28:直通限价单 52:止损限价单 54:止损单
time_in_force(int):订单时效性,有效时间类型[1-GTC(撤销前一直有效),4-FOK(极短时间全部成交，否则全部撤销),5-FAK(极短时间成交，剩余量全部撤销),6-GFD(当日有效),7-GTD(当日有效,必须设置过期时间) (default: {GTC})]
expireTime(str):过期时间</p>
<p>price(float):挂单价</p>
<p>side(string):持仓方向–&gt;SideEnum[B-买入,S-卖出]  (default: {None})</p>
<p>effect(int):开平仓类型–&gt;EffectEnum[0-中性,1-开仓,2-平仓]</p>
<p>quantity(int):下单量</p>
<p>amount(float):交易金额</p>
<p>tradedQuantity(int):成交量</p>
<p>orderStatus(int):订单状态–&gt;OrderStatusEnum[0-初始化,1-运行中,2-订单拒绝,3-开仓成交,5-订单已超时,6-订单撤销中,7-交易已撤销,8-已结束,9-已提交,99-未明]</p>
<p>createTime(int):创建时间</p>
<p>inOutMarket(string):执行市场–&gt;InOutMarketEnum[1-内部市场,2-外部市场,3-内/外部市场]</p>
<p>errorMsg(string):异常信息</p>
<p>tradedAvgPrice(float):成交均价</p>
<p>tradedAvgYtm(float):成交收益率均价(债券)</p>
<p>hedgeFlag(string):投机套保标识(期货专业))–&gt;HedgeFlagEnum[1-普通,2-投机,3-套保]</p>
<p>intention(string):交易意图</p>
<p>valueDate(string):起息日</p>
<p>maturityDate(string):到期日</p>
<p>closeOrderId(str):逐笔模式平仓订单id</p>
<p>posType(string):逐笔模式–&gt;PosTypeEnum[0-不启用(默认),1-启用]</p>
<p>warnPrice(float):预警价</p>
<p>stopPrice(float):止损价</p>
<p>ctimeStamp(long):生成数据纳秒</p>
<p>stopLossPrice(float):平仓止损价</p>
<p>takeProfitPrice(float):平仓止盈价</p>
<p>closeTradedQuantity(int):平仓成交量</p>
<p>closeAmount(int):平仓交易金额</p>
<p>closeTradedAvgPrice(float):平仓成交价格</p>
<p>closeTradedAvgYtm(float):平仓收益率(债券)</p>
<p>bp(int): 容忍点差</p>
<p>quoteId(str): 报价id</p>
<p>otherAgencyId(str): 对手方机构21位码</p>
<p>otherAgencyName(str): 对手方机构简称</p>
<p>otherTraderId(str): 对手方交易员id</p>
<p>otherTraderName(str): 对手方交易员名称</p>
<p>bpPrice(float):</p>
<p>bondQuoteType(int): 报价方式 9-连续匹配 10-集中匹配</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">deal</span><span class="o">.</span><span class="n">get_order</span><span class="p">(</span><span class="mi">216867535650689024</span><span class="p">)</span>
<span class="go">    {&#39;channelCode&#39;: &#39;X-SWAP-RT_HO&#39;, &#39;symbol&#39;: &#39;Shibor3M_6M&#39;, &#39;orderType&#39;: 2, &#39;timeInForce&#39;: 6, &#39;expireTime&#39;: None,</span>
<span class="go">    &#39;price&#39;: 1.0, &#39;side&#39;: &#39;S&#39;, &#39;effect&#39;: 0, &#39;quantity&#39;: 50000000, &#39;amount&#39;: 50000000.0, &#39;tradedQuantity&#39;: 50000000.0,</span>
<span class="go">    &#39;orderStatus&#39;: &#39;8&#39;, &#39;createTime&#39;: &#39;20240111181652&#39;, &#39;inOutMarket&#39;: 1, &#39;errorMsg&#39;: &#39;&#39;, &#39;tradedAvgPrice&#39;: 1.0, &#39;tradedAvgYtm&#39;: 1.0,</span>
<span class="go">    &#39;hedgeFlag&#39;: &#39;1&#39;, &#39;intention&#39;: None, &#39;valueDate&#39;: None, &#39;maturityDate&#39;: None, &#39;closeOrderId&#39;: None, &#39;posType&#39;: 0,</span>
<span class="go">    &#39;warnPrice&#39;: None, &#39;stopPrice&#39;: None, &#39;ctimeStamp&#39;: 1704968212213, &#39;rpType&#39;: 0,</span>
<span class="go">    &#39;bondQuoteType&#39;: 9, &#39;stopLossPrice&#39;: None, &#39;takeProfitPrice&#39;: None, &#39;closeTradedQuantity&#39;: None, &#39;closeAmount&#39;: None,</span>
<span class="go">    &#39;closeTradedAvgPrice&#39;: None, &#39;closeTradedAvgYtm&#39;: None, &#39;bp&#39;: None, &#39;bpPrice&#39;: None, &#39;quoteId&#39;: None, &#39;otherAgencyId&#39;: None,</span>
<span class="go">    &#39;otherAgencyName&#39;: None, &#39;otherTraderId&#39;: None, &#39;otherTraderName&#39;: None, &#39;id&#39;: &#39;Order444925856947638272&#39;}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="deal.get_orders">
<span class="sig-prename descclassname"><span class="pre">外汇.deal.</span></span><span class="sig-name descname"><span class="pre">get_orders</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">channel_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#deal.get_orders" title="Link to this definition"></a></dt>
<dd><p>批量获取进行中的委托订单</p>
<dl>
<dt>参数:</dt><dd><p>channel_code(string):市场代码
symbol(string):合约名 (default: {None})
side(string):持仓方向–&gt;SideEnum{B-买入,S-卖出} (default: {None})</p>
</dd>
<dt>返回:</dt><dd><p>Returns:list
[{</p>
<blockquote>
<div><p>id(str):订单ID</p>
<p>channelCode(string):渠道</p>
<p>symbol(string):合约</p>
<p>order_type (int): 订单类型:0:点击单 2:限价单(默认) 15:SOR单 28:直通限价单 52:止损限价单 54:止损单
time_in_force(int):订单时效性,有效时间类型[1-GTC(撤销前一直有效),4-FOK(极短时间全部成交，否则全部撤销),5-FAK(极短时间成交，剩余量全部撤销),6-GFD(当日有效),7-GTD(当日有效,必须设置过期时间) (default: {GTC})]
expireTime(str):过期时间</p>
<p>price(float):挂单价</p>
<p>side(string):持仓方向–&gt;SideEnum[B-买入,S-卖出]  (default: {None})</p>
<p>effect(int):开平仓类型–&gt;EffectEnum[0-中性,1-开仓,2-平仓]</p>
<p>quantity(int):下单量</p>
<p>amount(float):交易金额</p>
<p>tradedQuantity(int):成交量</p>
<p>orderStatus(int):订单状态–&gt;OrderStatusEnum[0-初始化,1-运行中,2-订单拒绝,3-开仓成交,5-订单已超时,6-订单撤销中,7-交易已撤销,8-已结束,9-已提交,99-未明]</p>
<p>createTime(int):创建时间</p>
<p>inOutMarket(string):执行市场–&gt;InOutMarketEnum[1-内部市场,2-外部市场,3-内/外部市场]</p>
<p>errorMsg(string):异常信息</p>
<p>tradedAvgPrice(float):成交均价</p>
<p>tradedAvgYtm(float):成交收益率均价(债券)</p>
<p>hedgeFlag(string):投机套保标识(期货专业))–&gt;HedgeFlagEnum[1-普通,2-投机,3-套保]</p>
<p>intention(string):交易意图</p>
<p>valueDate(string):起息日</p>
<p>maturityDate(string):到期日</p>
<p>closeOrderId(str):逐笔模式平仓订单id</p>
<p>posType(string):逐笔模式–&gt;PosTypeEnum[0-不启用(默认),1-启用]</p>
<p>warnPrice(float):预警价</p>
<p>stopPrice(float):止损价</p>
<p>ctimeStamp(long):生成数据纳秒</p>
<p>stopLossPrice(float):平仓止损价</p>
<p>takeProfitPrice(float):平仓止盈价</p>
<p>closeTradedQuantity(int):平仓成交量</p>
<p>closeAmount(int):平仓交易金额</p>
<p>closeTradedAvgPrice(float):平仓成交价格</p>
<p>closeTradedAvgYtm(float):平仓收益率(债券)</p>
<p>bp(int): 容忍点差</p>
<p>quoteId(str): 报价id</p>
<p>otherAgencyId(str): 对手方机构21位码</p>
<p>otherAgencyName(str): 对手方机构简称</p>
<p>otherTraderId(str): 对手方交易员id</p>
<p>otherTraderName(str): 对手方交易员名称</p>
<p>bpPrice(float):</p>
<p>bondQuoteType(int): 报价方式 9-连续匹配 10-集中匹配</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">deal</span><span class="o">.</span><span class="n">get_orders</span><span class="p">()</span>
<span class="go">    [{&#39;channelCode&#39;: &#39;X-SWAP-RT_HO&#39;, &#39;symbol&#39;: &#39;Shibor3M_6M&#39;, &#39;orderType&#39;: 2, &#39;timeInForce&#39;: 6, &#39;expireTime&#39;: None,</span>
<span class="go">    &#39;price&#39;: 1.0, &#39;side&#39;: &#39;S&#39;, &#39;effect&#39;: 0, &#39;quantity&#39;: 50000000, &#39;amount&#39;: 50000000.0, &#39;tradedQuantity&#39;: 50000000.0,</span>
<span class="go">    &#39;orderStatus&#39;: &#39;8&#39;, &#39;createTime&#39;: &#39;20240111181652&#39;, &#39;inOutMarket&#39;: 1, &#39;errorMsg&#39;: &#39;&#39;, &#39;tradedAvgPrice&#39;: 1.0, &#39;tradedAvgYtm&#39;: 1.0,</span>
<span class="go">    &#39;hedgeFlag&#39;: &#39;1&#39;, &#39;intention&#39;: None, &#39;valueDate&#39;: None, &#39;maturityDate&#39;: None, &#39;closeOrderId&#39;: None, &#39;posType&#39;: 0,</span>
<span class="go">    &#39;warnPrice&#39;: None, &#39;stopPrice&#39;: None, &#39;ctimeStamp&#39;: 1704968212213, &#39;rpType&#39;: 0,</span>
<span class="go">    &#39;bondQuoteType&#39;: 9, &#39;stopLossPrice&#39;: None, &#39;takeProfitPrice&#39;: None, &#39;closeTradedQuantity&#39;: None, &#39;closeAmount&#39;: None,</span>
<span class="go">    &#39;closeTradedAvgPrice&#39;: None, &#39;closeTradedAvgYtm&#39;: None, &#39;bp&#39;: None, &#39;bpPrice&#39;: None, &#39;quoteId&#39;: None, &#39;otherAgencyId&#39;: None,</span>
<span class="go">    &#39;otherAgencyName&#39;: None, &#39;otherTraderId&#39;: None, &#39;otherTraderName&#39;: None, &#39;id&#39;: &#39;Order444925856947638272&#39;,</span>
<span class="go">    }]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="deal.to_order">
<span class="sig-prename descclassname"><span class="pre">外汇.deal.</span></span><span class="sig-name descname"><span class="pre">to_order</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">side</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quantity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">order_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">in_out_market</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">channel_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time_in_force</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expire_time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hedge_flag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'1'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">intention</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">warn_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maturity_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close_order_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">currency</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bond_quote_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">9</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop_loss_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">take_profit_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bp</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#deal.to_order" title="Link to this definition"></a></dt>
<dd><p>下发新的订单</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>symbol</strong> (<em>string</em>) – 合约唯一代码</p></li>
<li><p><strong>side</strong> (<em>string</em>) – 交易方向     B:买 S:卖</p></li>
<li><p><strong>price</strong> (<em>double</em>) – 报单价格 (default: {None})</p></li>
<li><p><strong>quantity</strong> (<em>double</em>) – 报单总数量,贵金属为手数</p></li>
<li><p><strong>effect</strong> (<em>int</em>) – 开平仓类型:0:中性 1:开仓 2:平仓 3:平今, 4:平昨 (default: {None})</p></li>
<li><p><strong>order_type</strong> (<em>int</em>) – 订单类型:0:点击单 2:限价单(默认) 15:SOR单 28:直通限价单 50:择优询价单 52:止损限价单 54:止损单</p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>in_out_market</strong> (<em>string</em>) – 执行市场–&gt;InOutMarketEnum[1-内部市场,2-外部市场,3-内/外部市场]</p></li>
<li><p><strong>channel_code</strong> (<em>string</em>) – 交易渠道 (default: {None})</p></li>
<li><p><strong>time_in_force</strong> (<em>int</em>) – 订单时效性,有效时间类型, sor和click订单该字段无效[1-GTC(撤销前一直有效),4-FOK(极短时间全部成交，否则全部撤销),5-FAK(极短时间成交，剩余量全部撤销),6-GFD(当日有效),7-GTD(当日有效,必须设置过期时间); (default: {GTC})]</p></li>
<li><p><strong>expire_time</strong> (<em>str</em>) – 订单过期时间 当时效性为 GTC时 格式为:YYYYMMDDHHmmss; 当时效性为 GFD时 格式为:HHmmss;</p></li>
<li><p><strong>hedge_flag</strong> (<em>string</em>) – 投机套保标识(期货专有))        1:普通(默认) 2:投机 3:套保 (default: {1})</p></li>
<li><p><strong>intention</strong> (<em>string</em>) – 交易意图  可以根据业务场景自定义内容 (default: {None})</p></li>
<li><p><strong>warn_price</strong> (<em>double</em>) – 止损预警价        止损限价单使用 (default: {None})</p></li>
<li><p><strong>stop_price</strong> (<em>double</em>) – 止损价 (default: {None})</p></li>
<li><p><strong>value_date</strong> (<em>string</em>) – 近端起息日/近端交割日  掉期交易使用 格式: yyyyMMdd (default: {None})</p></li>
<li><p><strong>maturity_date</strong> (<em>string</em>) – 远端交割日     掉期交易使用 格式: yyyyMMdd (default: {None})</p></li>
<li><p><strong>close_order_id</strong> (<em>str</em>) – 平仓订单ID      逐笔模式ID (default: {None})</p></li>
<li><p><strong>pos_type</strong> (<em>int</em>) – 逐笔模式      0:不启用(默认) 1:启用 (default: {None})</p></li>
<li><p><strong>currency</strong> (<em>string</em>) – 使用货币 (default: {None})</p></li>
<li><p><strong>bond_quote_type</strong> – 9-连续匹配 10-集中匹配</p></li>
<li><p><strong>stop_loss_price</strong> (<em>float</em>) – 止损平仓价</p></li>
<li><p><strong>take_profit_price</strong> (<em>float</em>) – 止盈平仓价</p></li>
<li><p><strong>bp</strong> (<em>int</em>) – 容忍滑点 (现券以及算法订单使用。当发送现券订单时,不输入默认10bp; 非算法单默认为0)</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>str: 订单唯一ID号, 如果订单号为None或者0 ,说明交易未下单,请查看策略日志</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span> <span class="o">=</span> <span class="n">deal</span><span class="o">.</span><span class="n">to_order</span><span class="p">(</span><span class="n">context</span><span class="o">.</span><span class="n">ptparam</span><span class="o">.</span><span class="n">symbol</span><span class="p">,</span>
<span class="go">                side,</span>
<span class="go">                data.bestBid * 0.95 if side == &quot;S&quot; else data.bestAsk * 1.05,</span>
<span class="go">                abs(quantity),</span>
<span class="go">                order_type=2,</span>
<span class="go">                effect=0,</span>
<span class="go">                in_out_market=2,</span>
<span class="go">                channel_code=context.ptparam.source,</span>
<span class="go">                time_in_force=5)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="index.html" class="btn btn-neutral float-left" title="外汇API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="md.html" class="btn btn-neutral float-right" title="行情API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>