<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>资金账号 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="配置参数" href="param.html" />
    <link rel="prev" title="日志API" href="qlog.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="public.html">公共方法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="date.html">时间API</a></li>
<li class="toctree-l2"><a class="reference internal" href="qlog.html">日志API</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">资金账号</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#funds.get_funds"><code class="docutils literal notranslate"><span class="pre">get_funds()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#funds.get_market_info"><code class="docutils literal notranslate"><span class="pre">get_market_info()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="param.html">配置参数</a></li>
<li class="toctree-l2"><a class="reference internal" href="scheduler.html">定时任务</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="public.html">公共方法</a></li>
      <li class="breadcrumb-item active">资金账号</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/funds.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-funds">
<span id="id1"></span><h1>资金账号<a class="headerlink" href="#module-funds" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="funds.get_funds">
<span class="sig-prename descclassname"><span class="pre">funds.</span></span><span class="sig-name descname"><span class="pre">get_funds</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#funds.get_funds" title="Link to this definition"></a></dt>
<dd><p>CFETS发送订单确认请求给做市方</p>
<p>Returns:dict 资金账户对象</p>
<p>{</p>
<blockquote>
<div><p>funds:{</p>
<blockquote>
<div><p>net: 净值</p>
<p>freeze_amt: 冻结金额</p>
<p>commission: 佣金</p>
<p>ccy: 结算货币</p>
<p>realizedPL: 实际损益</p>
<p>init_money: 初始化资金</p>
</div></blockquote>
<p>}</p>
<p>net_pre_freeze:[ 净值模式的预占</p>
<blockquote>
<div><p>{</p>
<blockquote>
<div><p>amt: 交易金额</p>
<p>cost: 成本价</p>
<p>quantity: 交易量</p>
<p>side: 交易方向</p>
<p>symbol: 合约</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<p>]</p>
<p>oc_pre_freeze:[] 开平仓模式的预占</p>
<p>net_freeze:[] 净值模式的实占</p>
<p>oc_freeze:[] 开平仓模式的实占</p>
</div></blockquote>
<p>}</p>
<dl>
<dt>测试用例</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span> <span class="n">funds</span><span class="o">.</span><span class="n">get_funds</span><span class="p">()</span>
<span class="go">    {</span>
<span class="go">    &#39;funds&#39;: {</span>
<span class="go">        &#39;freeze_amt&#39;: 1500000.0,</span>
<span class="go">        &#39;commission&#39;: 0,</span>
<span class="go">        &#39;ccy&#39;: &#39;CNY&#39;,</span>
<span class="go">        &#39;realizedPL&#39;: 0,</span>
<span class="go">        &#39;unRealizedPL&#39;: 0.0,</span>
<span class="go">        &#39;init_money&#39;: 20000000.0,</span>
<span class="go">        &#39;equity&#39;: 20000000.0</span>
<span class="go">    },</span>
<span class="go">    &#39;net_pre_freeze&#39;: [</span>
<span class="go">        {</span>
<span class="go">        &#39;quantity&#39;: 0,</span>
<span class="go">        &#39;cost&#39;: 0,</span>
<span class="go">        &#39;amt&#39;: 0,</span>
<span class="go">        &#39;side&#39;: &#39;S&#39;,</span>
<span class="go">        &#39;symbol&#39;: &#39;Shibor3M_6M&#39;</span>
<span class="go">        }</span>
<span class="go">    ],</span>
<span class="go">    &#39;oc_pre_freeze&#39;: [],</span>
<span class="go">    &#39;net_freeze&#39;: [</span>
<span class="go">        {</span>
<span class="go">        &#39;amt&#39;: 1500000.0,</span>
<span class="go">        &#39;cost&#39;: 1.65353,</span>
<span class="go">        &#39;quantity&#39;: 50000000.0,</span>
<span class="go">        &#39;side&#39;: &#39;S&#39;,</span>
<span class="go">        &#39;symbol&#39;: &#39;Shibor3M_6M&#39;</span>
<span class="go">        }</span>
<span class="go">    ],</span>
<span class="go">    &#39;oc_freeze&#39;: []</span>
<span class="go">    }</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="funds.get_market_info">
<span class="sig-prename descclassname"><span class="pre">funds.</span></span><span class="sig-name descname"><span class="pre">get_market_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">channel_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">dict</span></span></span><a class="headerlink" href="#funds.get_market_info" title="Link to this definition"></a></dt>
<dd><p>获取市场信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>channel_code</strong> (<em>string</em>) – 渠道</p></li>
<li><p><strong>symbol</strong> (<em>string</em>) – 合约唯一代码</p></li>
</ul>
</dd>
<dt class="field-even">返回<span class="colon">:</span></dt>
<dd class="field-even"><p>交易市场规则</p>
</dd>
<dt class="field-odd">返回类型<span class="colon">:</span></dt>
<dd class="field-odd"><p>dict</p>
</dd>
</dl>
<dl>
<dt>测试用例</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">funds</span><span class="o">.</span><span class="n">get_market_info</span><span class="p">()</span>
<span class="go">    {&#39;Market_Code&#39;: &#39;X-SWAP-RT&#39;, &#39;DealType&#39;: &#39;ALL&#39;, &#39;Contract_Code&#39;: &#39;ALL&#39;, &#39;TradeTimeCode&#39;: &#39;ALL&#39;,</span>
<span class="go">    &#39;DealHolidayId&#39;: &#39;ALL&#39;, &#39;SettleHolidayId&#39;: &#39;ALL&#39;, &#39;MinPriceChange&#39;: 0.0, &#39;MinDealAmount&#39;: 1.0,</span>
<span class="go">    &#39;MaxDealAmount&#39;: 1.0, &#39;PriceUpLimit&#39;: 1.0, &#39;PriceDownLimit&#39;: 1.0, &#39;SpeculationBuyDepositRate&#39;: 3.0,</span>
<span class="go">    &#39;SpeculationSellDepositRate&#39;: 3.0, &#39;HedgingBuyDepositRate&#39;: 1.0, &#39;HedgingSellDepositRate&#39;: 1.0,</span>
<span class="go">    &#39;MinDealAmountChangeUnit&#39;: 6.0, &#39;COMMISSIONTYPE&#39;: &#39;&#39;, &#39;OPENCAST&#39;: 1.0, &#39;FLATTHISUNWINDCAST&#39;: 1.0,</span>
<span class="go">    &#39;FLATYESTERDAYCAST&#39;: 1.0, &#39;QUOTEDVALIDDIGIT&#39;: 6.0, &#39;COST_CURRENCY&#39;: &#39;CNY&#39;}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="qlog.html" class="btn btn-neutral float-left" title="日志API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="param.html" class="btn btn-neutral float-right" title="配置参数" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>