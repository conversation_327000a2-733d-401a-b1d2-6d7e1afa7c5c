<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>配置参数 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="定时任务" href="scheduler.html" />
    <link rel="prev" title="资金账号" href="funds.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="public.html">公共方法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="date.html">时间API</a></li>
<li class="toctree-l2"><a class="reference internal" href="qlog.html">日志API</a></li>
<li class="toctree-l2"><a class="reference internal" href="funds.html">资金账号</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">配置参数</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#param.get"><code class="docutils literal notranslate"><span class="pre">get()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#param.get_group_names"><code class="docutils literal notranslate"><span class="pre">get_group_names()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#param.get_matrix_np"><code class="docutils literal notranslate"><span class="pre">get_matrix_np()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#param.get_matrix_pb"><code class="docutils literal notranslate"><span class="pre">get_matrix_pb()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="scheduler.html">定时任务</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="public.html">公共方法</a></li>
      <li class="breadcrumb-item active">配置参数</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/param.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-param">
<span id="id1"></span><h1>配置参数<a class="headerlink" href="#module-param" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="param.get">
<span class="sig-prename descclassname"><span class="pre">param.</span></span><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">paramKey</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'默认参数'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#param.get" title="Link to this definition"></a></dt>
<dd><p>策略配置的参数获取函数,在策略维度使用客户端配置的启动参数</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>key</strong> (<em>str</em>) – 策略配置的参数名称</p></li>
<li><p><strong>paramKey</strong> (<em>str</em>) – 指定参数分组的key，即tab页名称</p></li>
</ul>
</dd>
<dt class="field-even">返回<span class="colon">:</span></dt>
<dd class="field-even"><p>策略配置的参数值</p>
</dd>
<dt class="field-odd">返回类型<span class="colon">:</span></dt>
<dd class="field-odd"><p>string</p>
</dd>
</dl>
<p>用例初始化数据
【name    名字     will】
【age     年龄     18  】</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">qlog</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">))</span>
<span class="go">    Will</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">qlog</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="n">param</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;名字&quot;</span><span class="p">))</span>
<span class="go">    Will</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="param.get_group_names">
<span class="sig-prename descclassname"><span class="pre">param.</span></span><span class="sig-name descname"><span class="pre">get_group_names</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#param.get_group_names" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="param.get_matrix_np">
<span class="sig-prename descclassname"><span class="pre">param.</span></span><span class="sig-name descname"><span class="pre">get_matrix_np</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sheet_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">paramKey</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'默认参数'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#param.get_matrix_np" title="Link to this definition"></a></dt>
<dd><p>策略配置的参数获取函数,在策略维度使用客户端配置的矩阵参数,由固定Excel模板导入数据.仅支持中英文大小写,下划线和中括号</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>key</strong> (<em>string</em>) – 策略配置的参数名称</p></li>
<li><p><strong>sheet_name</strong> (<em>string</em>) – Excel中sheet页的名称</p></li>
</ul>
</dd>
<dt class="field-even">返回<span class="colon">:</span></dt>
<dd class="field-even"><p>excel里sheet页里面 numpy结构数据</p>
</dd>
<dt class="field-odd">返回类型<span class="colon">:</span></dt>
<dd class="field-odd"><p>numpy</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="param.get_matrix_pb">
<span class="sig-prename descclassname"><span class="pre">param.</span></span><span class="sig-name descname"><span class="pre">get_matrix_pb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sheet_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">paramKey</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'默认参数'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#param.get_matrix_pb" title="Link to this definition"></a></dt>
<dd><p>策略配置的参数获取函数,在策略维度使用客户端配置的矩阵参数,由固定Excel模板导入数据.仅支持中英文大小写,下划线和中括号</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>key</strong> (<em>string</em>) – 策略配置的参数名称</p></li>
<li><p><strong>sheet_name</strong> (<em>string</em>) – Excel中sheet页的名称</p></li>
</ul>
</dd>
<dt class="field-even">返回<span class="colon">:</span></dt>
<dd class="field-even"><p>excel里sheet页里面 pandas结构数据</p>
</dd>
<dt class="field-odd">返回类型<span class="colon">:</span></dt>
<dd class="field-odd"><p>pandas</p>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="funds.html" class="btn btn-neutral float-left" title="资金账号" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="scheduler.html" class="btn btn-neutral float-right" title="定时任务" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>