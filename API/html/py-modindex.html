<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Python 模块索引 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
 

    <script>
      DOCUMENTATION_OPTIONS.COLLAPSE_INDEX = true;
    </script>


</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Python 模块索引</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

   <h1>Python 模块索引</h1>

   <div class="modindex-jumpbox">
   <a href="#cap-b"><strong>b</strong></a> | 
   <a href="#cap-d"><strong>d</strong></a> | 
   <a href="#cap-e"><strong>e</strong></a> | 
   <a href="#cap-f"><strong>f</strong></a> | 
   <a href="#cap-i"><strong>i</strong></a> | 
   <a href="#cap-o"><strong>o</strong></a> | 
   <a href="#cap-p"><strong>p</strong></a> | 
   <a href="#cap-q"><strong>q</strong></a> | 
   <a href="#cap-s"><strong>s</strong></a> | 
   <a href="#cap-t"><strong>t</strong></a> | 
   <a href="#cap-固"><strong>固</strong></a> | 
   <a href="#cap-外"><strong>外</strong></a>
   </div>

   <table class="indextable modindextable">
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-b"><td></td><td>
       <strong>b</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="base.html#module-base"><code class="xref">base</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-d"><td></td><td>
       <strong>d</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="date.html#module-date"><code class="xref">date</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-e"><td></td><td>
       <strong>e</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="enum_erayt.html#module-enum_erayt"><code class="xref">enum_erayt</code></a></td><td>
       <em></em></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-1" style="display: none" alt="-" /></td>
       <td>
       <code class="xref">equantapi</code></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="xrisk.html#module-equantapi.xrisk"><code class="xref">equantapi.xrisk</code></a></td><td>
       <em></em></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="event.html#module-event"><code class="xref">event</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-f"><td></td><td>
       <strong>f</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="funds.html#module-funds"><code class="xref">funds</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-i"><td></td><td>
       <strong>i</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="indicator_arsenals.html#module-indicator_arsenals"><code class="xref">indicator_arsenals</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-o"><td></td><td>
       <strong>o</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="object.html#module-object"><code class="xref">object</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-p"><td></td><td>
       <strong>p</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="param.html#module-param"><code class="xref">param</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-q"><td></td><td>
       <strong>q</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="qlog.html#module-qlog"><code class="xref">qlog</code></a></td><td>
       <em></em></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="quick_start.html#module-quick_start"><code class="xref">quick_start</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-s"><td></td><td>
       <strong>s</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="scheduler.html#module-scheduler"><code class="xref">scheduler</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-t"><td></td><td>
       <strong>t</strong></td><td></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="talib_erayt.html#module-talib_erayt"><code class="xref">talib_erayt</code></a></td><td>
       <em></em></td></tr>
     <tr>
       <td></td>
       <td>
       <a href="trade_arsenals.html#module-trade_arsenals"><code class="xref">trade_arsenals</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-固"><td></td><td>
       <strong>固</strong></td><td></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-2" style="display: none" alt="-" /></td>
       <td>
       <code class="xref">固收</code></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="bond/deal.html#module-.deal"><code class="xref">固收.deal</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="bond/maker.html#module-.maker"><code class="xref">固收.maker</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="bond/md.html#module-.md"><code class="xref">固收.md</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="bond/pos.html#module-.pos"><code class="xref">固收.pos</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="bond/signal.html#module-.signal"><code class="xref">固收.signal</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-外"><td></td><td>
       <strong>外</strong></td><td></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-3" style="display: none" alt="-" /></td>
       <td>
       <code class="xref">外汇</code></td><td>
       <em></em></td></tr>
     <tr class="cg-3">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="fx/deal.html#module-.deal"><code class="xref">外汇.deal</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-3">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="fx/maker.html#module-.maker"><code class="xref">外汇.maker</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-3">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="fx/md.html#module-.md"><code class="xref">外汇.md</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-3">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="fx/pos.html#module-.pos"><code class="xref">外汇.pos</code></a></td><td>
       <em></em></td></tr>
   </table>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>