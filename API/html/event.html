<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>交易事件 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="常用枚举和结构" href="enum_stu.html" />
    <link rel="prev" title="风控API" href="xrisk.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">交易事件</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#event.init"><code class="docutils literal notranslate"><span class="pre">init()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onBusinessDate"><code class="docutils literal notranslate"><span class="pre">onBusinessDate()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onData"><code class="docutils literal notranslate"><span class="pre">onData()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onOrder"><code class="docutils literal notranslate"><span class="pre">onOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onQuote"><code class="docutils literal notranslate"><span class="pre">onQuote()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onQuoteOrder"><code class="docutils literal notranslate"><span class="pre">onQuoteOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onRfqQuote"><code class="docutils literal notranslate"><span class="pre">onRfqQuote()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onRfqQuoteOrder"><code class="docutils literal notranslate"><span class="pre">onRfqQuoteOrder()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onRfqReq"><code class="docutils literal notranslate"><span class="pre">onRfqReq()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onTime"><code class="docutils literal notranslate"><span class="pre">onTime()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#event.onTrade"><code class="docutils literal notranslate"><span class="pre">onTrade()</span></code></a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">交易事件</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/event.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-event">
<span id="id1"></span><h1>交易事件<a class="headerlink" href="#module-event" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="event.init">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.init" title="Link to this definition"></a></dt>
<dd><p>init 中初始化一些必要的参数，订阅数据
初始化方法 - 在回测和实时模拟交易只会在启动的时候触发一次。
你的算法会使用这个方法来设置你需要的各种初始化配置。
context 对象将会在你的算法的所有其他的方法之间进行传递以方便你可以拿取到。</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onBusinessDate">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onBusinessDate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onBusinessDate" title="Link to this definition"></a></dt>
<dd><p>产生切日事件触发事件驱动</p>
<dl>
<dt>参数:</dt><dd><dl>
<dt>context(class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>data(dict):</dt><dd><dl>
<dt>切日对象</dt><dd><p>{</p>
<p>‘sendDate’: ‘20230530’, #</p>
<p>‘sendTime’: ‘163000’, #</p>
<p>‘instrument’: ‘FX’, #</p>
<p>‘beforeDate’: 20230529, #</p>
<p>‘afterDate’: 20230530, #</p>
<p>‘nowTime’: 1598922000100 #</p>
<p>}</p>
</dd>
</dl>
</dd>
</dl>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onBusinessDate</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
<span class="go">        afterDate = data[&#39;afterDate&#39;]</span>
<span class="go">        pass</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onData">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onData" title="Link to this definition"></a></dt>
<dd><p>已订阅（subscribe）合约tick ，每次数据的更新会自动触发该方法的调用。
策略具体逻辑可在该方法内实现，包括交易信号的产生、订单的创建、风险管理等</p>
<dl>
<dt>参数:</dt><dd><dl>
<dt>context(class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>data(list):</dt><dd><p>[</p>
<blockquote>
<div><p>{“status”: “1”, # 状态码</p>
<p>“source”: “CFETS_LC”, # 渠道</p>
<p>“type”: “ODM_DEPTH”, # 数据类型</p>
<p>“symbol”: “EURUSDSP”, # 合约</p>
<p>“time”: 1532000820300, # 时间戳</p>
<p>“bestBid”: 1.16478, # 最优买价</p>
<p>“bestBidAmt”: 57, # 最优买量</p>
<p>“bestAsk”: 1.1649, # 最优卖价</p>
<p>“bestAskAmt”: 57, # 最优卖量</p>
<p>“asks”: [1.1649, 1.16496, 1.16502, 1.16507, 1.16513], # 卖价档位</p>
<p>“ask_vols”: [57, 57, 57, 57, 57], # 卖量档位</p>
<p>“bids”: [1.16478, 1.16472, 1.16466, 1.16461, 1.16455], # 买价档位</p>
<p>“bid_vols”: [57, 57, 57, 57, 57], # 买量档位</p>
<p>“limitUp”: “”,</p>
<p>“limitDown”: “”</p>
<p>}</p>
</div></blockquote>
<p>]</p>
</dd>
</dl>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onData</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">data</span><span class="p">):</span>
<span class="go">        tick = data[0]</span>
<span class="go">        bid =tick.bestBid</span>
<span class="go">        ask =tick.bestAsk</span>
<span class="go">        pass</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onOrder">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onOrder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">order</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onOrder" title="Link to this definition"></a></dt>
<dd><p>根据条件撤销委托挂单</p>
<dl>
<dt>参数:</dt><dd><dl>
<dt>context(class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>order(dict):</dt><dd><p>订单数据</p>
<p>{</p>
<p>id(str):订单ID</p>
<p>channelCode(string):渠道</p>
<p>symbol(string):合约</p>
<p>orderType(int):订单类型–&gt;OrderTypeEnum[0:点击单 2:限价单(默认) 15:SOR单 28:直通限价单 52:止损限价单 54:止损单]</p>
<p>timeInForce(int):订单时效性,有效时间类型[1-GTC(撤销前一直有效),4-FOK(极短时间全部成交，否则全部撤销),5-FAK(极短时间成交，剩余量全部撤销),6-GFD(当日闭市前有效),7-GTD(当日有效,必须设置过期时间)  (default: {GTC})]</p>
<p>expireTime(int):过期时间</p>
<p>price(float):挂单价</p>
<p>side(string):持仓方向–&gt;SideEnum[B-买入,S-卖出]  (default: {None})</p>
<p>effect(int):开平仓类型–&gt;EffectEnum[0-中性,1-开仓,2-平仓]</p>
<p>quantity(int):下单量</p>
<p>amount(float):交易金额</p>
<p>tradedQuantity(int):成交量</p>
<p>orderStatus(int):订单状态–&gt;OrderStatusEnum[0-初始化,1-运行中,2-订单拒绝,3-开仓成交,5-订单已超时,6-订单撤销中,7-交易已撤销,8-已结束,9-已提交,99-未明]</p>
<p>createTime(int):创建时间</p>
<p>inOutMarket(string):执行市场–&gt;InOutMarketEnum[1-内部市场,2-外部市场,3-内/外部市场]</p>
<p>errorMsg(string):异常信息</p>
<p>tradedAvgPrice(float):成交均价</p>
<p>tradedAvgYtm(float):成交收益率均价(债券)</p>
<p>hedgeFlag(string):投机套保标识(期货专业))–&gt;HedgeFlagEnum[1-普通,2-投机,3-套保]</p>
<p>intention(string):交易意图</p>
<p>valueDate(string):起息日</p>
<p>maturityDate(string):到期日</p>
<p>closeOrderId(str):逐笔模式平仓订单id</p>
<p>posType(string):逐笔模式–&gt;PosTypeEnum[0-不启用(默认),1-启用]</p>
<p>warnPrice(float):预警价</p>
<p>stopPrice(float):止损价</p>
<p>ctimeStamp(long):生成数据毫秒</p>
<p>stopLossPrice(float):平仓止损价</p>
<p>takeProfitPrice(float):平仓止盈价</p>
<p>closeTradedQuantity(int):平仓成交量</p>
<p>closeAmount(int):平仓交易金额</p>
<p>closeTradedAvgPrice(float):平仓成交价格</p>
<p>closeTradedAvgYtm(float):平仓收益率(债券)</p>
<p>bp(int): 容忍点差</p>
<p>quoteId(str): 报价id</p>
<p>ytm(str): 债券收益率报价(%) (default: {None})</p>
<p>otherAgencyId(str): 对手方机构21位码</p>
<p>otherAgencyName(str): 对手方机构简称</p>
<p>otherTraderId(str): 对手方交易员id</p>
<p>otherTraderName(str): 对手方交易员名称</p>
<p>}</p>
</dd>
</dl>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onOrder</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">order</span><span class="p">):</span>
<span class="go">        qlog.info(order)</span>
<span class="go">    {</span>
<span class="go">        &#39;channelCode&#39;: &#39;UBS_HO&#39;,</span>
<span class="go">        &#39;symbol&#39;: &#39;EURUSDSP&#39;,</span>
<span class="go">        &#39;orderType&#39;: 2,</span>
<span class="go">        &#39;timeInForce&#39;: 1,</span>
<span class="go">        &#39;expireTime&#39;: None,</span>
<span class="go">        &#39;price&#39;: 1.3,</span>
<span class="go">        &#39;side&#39;: &#39;S&#39;,</span>
<span class="go">        &#39;effect&#39;: 0,</span>
<span class="go">        &#39;quantity&#39;: 200000,</span>
<span class="go">        &#39;amount&#39;: 0,</span>
<span class="go">        &#39;tradedQuantity&#39;: 0,</span>
<span class="go">        &#39;orderStatus&#39;: &#39;2&#39;,</span>
<span class="go">        &#39;createTime&#39;: &#39;20240111152407&#39;,</span>
<span class="go">        &#39;inOutMarket&#39;: 3,</span>
<span class="go">        &#39;errorMsg&#39;: &#39;&#39;,</span>
<span class="go">        &#39;tradedAvgPrice&#39;: 0,</span>
<span class="go">        &#39;tradedAvgYtm&#39;: 1.0,</span>
<span class="go">        &#39;hedgeFlag&#39;: &#39;1&#39;,</span>
<span class="go">        &#39;intention&#39;: None,</span>
<span class="go">        &#39;valueDate&#39;: None,</span>
<span class="go">        &#39;maturityDate&#39;: None,</span>
<span class="go">        &#39;closeOrderId&#39;: None,</span>
<span class="go">        &#39;posType&#39;: 0,</span>
<span class="go">        &#39;warnPrice&#39;: None,</span>
<span class="go">        &#39;stopPrice&#39;: None,</span>
<span class="go">        &#39;ctimeStamp&#39;: 1704957847416,</span>
<span class="go">        &#39;seq&#39;: 0,</span>
<span class="go">        &#39;rpType&#39;: 0,</span>
<span class="go">        &#39;symbolCname&#39;: &#39;EURUSDSP&#39;,</span>
<span class="go">        &#39;bondQuoteType&#39;: 9,</span>
<span class="go">        &#39;stopLossPrice&#39;: None,</span>
<span class="go">        &#39;takeProfitPrice&#39;: None,</span>
<span class="go">        &#39;closeTradedQuantity&#39;: 0,</span>
<span class="go">        &#39;closeAmount&#39;: 0,</span>
<span class="go">        &#39;closeTradedAvgPrice&#39;: 0,</span>
<span class="go">        &#39;closeTradedAvgYtm&#39;: 0,</span>
<span class="go">        &#39;bp&#39;: None,</span>
<span class="go">        &#39;bpPrice&#39;: None,</span>
<span class="go">        &#39;quoteId&#39;: None,</span>
<span class="go">        &#39;ytm&#39;: 0.1,</span>
<span class="go">        &#39;otherAgencyId&#39;: None,</span>
<span class="go">        &#39;otherAgencyName&#39;: None,</span>
<span class="go">        &#39;otherTraderId&#39;: None,</span>
<span class="go">        &#39;otherTraderName&#39;: None,</span>
<span class="go">        &#39;id&#39;: &#39;Order444882385620705280&#39;</span>
<span class="go">    }</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onQuote">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onQuote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quote</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onQuote" title="Link to this definition"></a></dt>
<dd><p>做市商对外的报价如果被风控拒绝或者被交易中心拒绝，将触发onQuote事件</p>
<dl>
<dt>必选参数:</dt><dd><dl>
<dt>context (class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>quote (dict):</dt><dd><dl>
<dt>报价对象</dt><dd><dl>
<dt>外汇做市</dt><dd><p>{</p>
<blockquote>
<div><p>‘quoteId’: ‘b51ed597-9f7f-3fb4-b8c9-fd77e1aab906’,# 报价id</p>
<p>‘status’: ‘2’,# 状态</p>
<p>‘symbol’: ‘EURUSDSP’,# 合约</p>
<p>‘time’: 1705043184689,# 时间戳(毫秒)</p>
<p>‘floorCode’: ‘CFETS-ESP’, #, floorCode</p>
<p>‘quoteTypeStr’: ‘ESP’, # 报价类型</p>
<p>‘quoteDateTime’: 20240112150624000, # 报价时间YYYYMMDDHHmmss</p>
<p>‘makerDepths’: [ # 报价信息,即to_qutoe的报价信息</p>
<blockquote>
<div><p>{</p>
<p>‘positionNo’: 1,</p>
<p>‘bidAmt’: 1000000.0,</p>
<p>‘askAmt’: 1000000.0,</p>
<p>‘bid’: 21.1951,</p>
<p>‘ask’: 21.1959,</p>
<p>‘level’: 1</p>
<p>}</p>
</div></blockquote>
<p>],</p>
<p>‘errorText’: ‘未找到对应的渠道数据，channelCode = CFETS-ESP’ # 失败原因</p>
<p>}</p>
</div></blockquote>
</dd>
<dt>债券做市</dt><dd><p>{</p>
<blockquote>
<div><p>‘quoteId’: ‘883d7924-8ef0-315a-93d7-7c736d46c15d’, # 报价id</p>
<p>‘makerDepths’: [ # 报价信息,即to_qutoe的报价信息</p>
<blockquote>
<div><p>{</p>
<p>‘bid’: 119.9371,</p>
<p>‘bidAmt’: 10000000.0,</p>
<p>‘ask’: 119.9805,</p>
<p>‘askAmt’: 10000000.0</p>
<p>‘bidYield’:2.1</p>
<p>‘askYield’:2.2</p>
<p>}</p>
</div></blockquote>
<p>],</p>
<p>‘quoteDateTime’: ‘20240112145715’, # 报价时间YYYYMMDDHHmmss</p>
<p>‘symbol’: ‘160017_T+1’, # 合约</p>
<p>‘floorCode’: None, # floorCode</p>
<p>‘errorText’: ‘报价失效：策略未绑定报价’, # 失败原因</p>
<p>‘status’: 1 # 报价状态 1-失败</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
</dl>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onQuote</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">quote</span><span class="p">):</span>
<span class="go">        maker.to_quote_order_confirm(id, &quot;F&quot;)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onQuoteOrder">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onQuoteOrder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quoteOrder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onQuoteOrder" title="Link to this definition"></a></dt>
<dd><p>做市商对外的报价如果被交易对手点击成交请求，将触发onQuoteOrder事件,
做市商需要通过to_quote_order_confirm来接收或者拒绝该成交请求,
本币做市不会触发该事件</p>
<dl>
<dt>必选参数:</dt><dd><dl>
<dt>context (class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>quoteOrder (dict):</dt><dd><dl>
<dt>报价交易对象</dt><dd><p>{</p>
<blockquote>
<div><p>“floorCode”: “LP_ESP1”,</p>
<p>“id”: 223483495955697664,</p>
<p>“orderStatus”: 2,</p>
<p>“symbol”: “EURUSDSP”,</p>
<p>“products”: “FXSPOT”,</p>
<p>“side”: “B”,</p>
<p>“orderType”: 1,</p>
<p>“quantity”: 196000000.0,</p>
<p>“tradeQuantity”: 0,</p>
<p>“price”: 21.18294,</p>
<p>“farPrice”: None,</p>
<p>“spotRate”: None,</p>
<p>“points”: None,</p>
<p>“farPoints”: None,</p>
<p>“createTime”: 1625198940000,</p>
<p>“partyId”: “yt”,</p>
<p>“partyRoleId”: “yt”</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onQuoteOrder</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">quoteOrder</span><span class="p">):</span>
<span class="go">        maker.to_rfq_quote(&quot;EURUSDSP&quot;, 7.1, 7.2)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onRfqQuote">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onRfqQuote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rfqQuote</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onRfqQuote" title="Link to this definition"></a></dt>
<dd><p>做市商对外的报价如果被风控拦截或者被交易中心拒绝，将触发onRfqQuote事件</p>
<dl>
<dt>必选参数:</dt><dd><dl>
<dt>context (class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>rfqQuote (dict):</dt><dd><dl>
<dt>询价交易对象</dt><dd><dl>
<dt>外汇做市</dt><dd><p>{</p>
<blockquote>
<div><p>交易分组: floorCode=’LP_IND1’</p>
<p>状态: status=1</p>
<p>请求id: reqId=216869925133619200</p>
<p>合约渠道: source=”CFETS_LP”</p>
<p>策略合约: symbol=’USDCNHSP’</p>
<p>买价: bid=’6.98749’</p>
<p>买量: bidAmt=3</p>
<p>卖价: ask=6.98784</p>
<p>卖量: askAmt=3</p>
<p>买入即期价格: bidSpotRate=6.98749</p>
<p>卖出即期价格: askSpotRate=6.98784</p>
<p>买入远期点: bidFwdPoints=None</p>
<p>卖出远期点: askFwdPoints=None</p>
<p>买入远期全价: farBid=6.98749</p>
<p>卖出远期全价: farAsk=6.98784</p>
<p>远端买入远期点: farBidFwdPoints=None</p>
<p>远端卖出远期点: farAskFwdPoints=None</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>债券做市 仿真相同</dt><dd><p>{</p>
<blockquote>
<div><p>“reqId”: 询价id</p>
<p>“quoteId”: 报价id</p>
<p>“status”: 报价状态 1-失败</p>
<p>“symbol”: 合约</p>
<p>“errorText”: 失败原因</p>
<p>“quoteDateTime”: 报价时间YYYYMMDDHHmmss</p>
<p>“price”: 净价</p>
<p>“yield”: 收益率</p>
<p>“strikeYield”: 行权收益率</p>
<p>“orderQty”: 请求报价回复量</p>
<p>“validTime”: 报价有效时间</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
</dl>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onRfqQuote</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">rfqQuote</span><span class="p">):</span>
<span class="go">        pass</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onRfqQuoteOrder">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onRfqQuoteOrder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rfqQuoteOrder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onRfqQuoteOrder" title="Link to this definition"></a></dt>
<dd><p>做市商对外的报价如果被交易对手点击成交请求，将触发onRfqQuoteOrder事件,做市商需要通过to_rfq_quote_order_confirm来接收或者拒绝该成交请求
本币做市不会触发该事件</p>
<dl>
<dt>必选参数:</dt><dd><dl>
<dt>context (class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>rfqQuoteOrder(dict):</dt><dd><dl>
<dt>询价成交对象</dt><dd><p>{</p>
<blockquote>
<div><p>订单编号: id=216870196848627712</p>
<p>交易分组: floorCode=’LP_IND1’</p>
<p>策略合约: symbol=’USDCNHSP’</p>
<p>订单类型: orderType [2:限价单(默认) 6:询价交易 50:择优询价交易单 51:择优限价单 52:停止限价单 53:冰山订单 54:止损单 (default)]</p>
<p>买卖方向: side=’B’</p>
<p>订单状态: orderStatus=2</p>
<p>开平仓类型: effect=0 [0-中性 1-开仓 2-平仓]</p>
<p>交易量: quantity=3</p>
<p>价格: price=6.98749</p>
<p>远期价格: farPrice=6.98749</p>
<p>即期价格: spotRate=6.98749</p>
<p>点: points=0</p>
<p>远期点: farPoints=0</p>
<p>创建时间: createTime=1532043000101</p>
<p>本方交易员: partyId=’yt’</p>
<p>对手方交易员: partyRoleId=’yt’</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onRfqQuoteOrder</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">rfqQuoteOrder</span><span class="p">):</span>
<span class="go">        trade_id = data.id</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onRfqReq">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onRfqReq</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rfqReq</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onRfqReq" title="Link to this definition"></a></dt>
<dd><p>对手方对做市商IND报价发起询价后,触发onRfqReq事件</p>
<dl>
<dt>必选参数:</dt><dd><dl>
<dt>context (class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>rfqReq (dict):</dt><dd><p>{</p>
<blockquote>
<div><p>“floorCode”: 渠道         CFETS-银行间 CFAE-北金所 BC-债券通</p>
<p>“reqId”: 询价id</p>
<p>“expireTime”: YYYYMMDDHHmmss</p>
<p>“symbol”: 合约</p>
<p>“side”: 本方交易方向 B-买入 S-卖出</p>
<p>“quoteSize”: 询价量</p>
<p>“partyId”: 对手方机构ID      机构21位码</p>
<p>“partyRoleId”:对手方交易员ID</p>
<p>“status”: 1-询价请求 2-询价撤销</p>
<p>‘products’: 产品小类</p>
<p>‘quote_status’: 状态</p>
<p>‘createTime’: 接收询价的时间戳</p>
<p>‘click_status’: 点击状态 0-未被点击 1-已被点击</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onRfqReq</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">rfqReq</span><span class="p">):</span>
<span class="go">        qlog.info_f(&quot;回价失败, 失败原因{}&quot;, rfqQuote)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onTime">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onTime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onTime" title="Link to this definition"></a></dt>
<dd><p>定时触发事件</p>
<dl>
<dt>参数:</dt><dd><dl class="simple">
<dt>context(class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>time(str):</dt><dd><p>定时器触发时间</p>
</dd>
<dt>name(str):</dt><dd><p>定时器名称 跟scheduler注册的定时器名称一样</p>
</dd>
</dl>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">scheduler</span><span class="o">.</span><span class="n">run_daily</span><span class="p">(</span><span class="s2">&quot;my_job&quot;</span><span class="p">,</span> <span class="s2">&quot;160000&quot;</span><span class="p">)</span> <span class="c1">#需要在init方法中先使用scheduler设置好定时器</span>
<span class="go">    def onTime(context,time,name):</span>
<span class="go">        if name ==&#39;my_job&#39;:</span>
<span class="go">            #做一些事情，如平仓，查询敞口等</span>
<span class="go">        pass</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="event.onTrade">
<span class="sig-prename descclassname"><span class="pre">event.</span></span><span class="sig-name descname"><span class="pre">onTrade</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">trade</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#event.onTrade" title="Link to this definition"></a></dt>
<dd><p>产生成交后触发事件驱动</p>
<dl>
<dt>参数:</dt><dd><dl>
<dt>context(class):</dt><dd><p>连接上下文的环境对象，该对象将会在你的算法策略的任何方法之间做传递。
用户可以通过context定义多种自己需要的属性，也可以查看context固有属性</p>
</dd>
<dt>trade(dict):</dt><dd><p>{</p>
<blockquote>
<div><p>‘id’: 216870196848627712, # 交易ID</p>
<p>‘orderId’: 216870196848627712, # 订单ID</p>
<p>‘channelCode’: ‘CFETS_LC’, # 交易渠道</p>
<p>‘symbol’: ‘EURUSDSP’, # 合约</p>
<p>‘valueDate’: ‘20180722’, # 起息日</p>
<p>‘side’: ‘B’, # 交易方向</p>
<p>‘effect’: 0, # 开平仓类型:[0-中性 1-开仓 2-平仓]</p>
<p>‘price’: 1.1657599999999997, # 价格</p>
<p>‘ytm’: 0.7, # 债券收益率价格</p>
<p>‘quantity’: 10, # 下单量</p>
<p>‘amount’: 11.657599999999997, # 交易金额，外汇掉期合约显示同quantity字段</p>
<p>‘tradeTime’: ‘20180720073000’, # 交易时间</p>
<p>‘closeOrderId’: None, # 逐笔模式平仓订单id</p>
<p>‘nearPrice’: 1.343, # 掉期近端成交价</p>
<p>‘farPrice’: 1.443, # 掉期远端成交价</p>
<p>‘nearDate’: ‘20240601’, # 掉期近端到期日</p>
<p>‘farDate’: ‘20240608’ # 掉期远端到期日</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
</dd>
<dt>返回值:</dt><dd><p>无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span><span class="w"> </span><span class="nf">onTrade</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="n">trade</span><span class="p">):</span>
<span class="go">        {&#39;id&#39;: 216870196848627712, &#39;orderId&#39;: 216870196848627712, &#39;channelCode&#39;: &#39;CFETS_LC&#39;,</span>
<span class="go">        &#39;symbol&#39;: &#39;EURUSDSP&#39;, &#39;valueDate&#39;: &#39;20180722&#39;,</span>
<span class="go">        &#39;side&#39;: &#39;B&#39;, &#39;effect&#39;: 0, &#39;price&#39;: 1.1657599999999997, &#39;ytm&#39;: 0.7,&#39;quantity&#39;: 10,</span>
<span class="go">        &#39;amount&#39;: 1165759.9999999998, &#39;tradeTime&#39;: &#39;20180720073000&#39;, &#39;closeOrderId&#39;: None,</span>
<span class="go">        }</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="xrisk.html" class="btn btn-neutral float-left" title="风控API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="enum_stu.html" class="btn btn-neutral float-right" title="常用枚举和结构" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>