Search.setIndex({"docnames": ["arsenals_method", "base", "bond/deal", "bond/index", "bond/maker", "bond/md", "bond/pos", "bond/signal", "date", "enum_erayt", "enum_stu", "event", "funds", "fx/deal", "fx/index", "fx/maker", "fx/md", "fx/pos", "index", "indicator_arsenals", "modules", "mt4", "object", "param", "public", "qlog", "quick_start", "scheduler", "talib_erayt", "trade_arsenals", "trade_method", "xrisk"], "filenames": ["arsenals_method.rst", "base.rst", "bond/deal.rst", "bond/index.rst", "bond/maker.rst", "bond/md.rst", "bond/pos.rst", "bond/signal.rst", "date.rst", "enum_erayt.rst", "enum_stu.rst", "event.rst", "funds.rst", "fx/deal.rst", "fx/index.rst", "fx/maker.rst", "fx/md.rst", "fx/pos.rst", "index.rst", "indicator_arsenals.rst", "modules.rst", "mt4.rst", "object.rst", "param.rst", "public.rst", "qlog.rst", "quick_start.rst", "scheduler.rst", "talib_erayt.rst", "trade_arsenals.rst", "trade_method.rst", "xrisk.rst"], "titles": ["\u6b66\u5668\u5e93", "\u57fa\u7840API", "\u8ba2\u5355API", "\u56fa\u6536API", "\u505a\u5e02API", "\u884c\u60c5API", "\u6301\u4ed3API", "\u4fe1\u53f7API", "\u65f6\u95f4API", "\u5e38\u7528\u679a\u4e3e", "\u5e38\u7528\u679a\u4e3e\u548c\u7ed3\u6784", "\u4ea4\u6613\u4e8b\u4ef6", "\u8d44\u91d1\u8d26\u53f7", "\u8ba2\u5355API", "\u5916\u6c47API", "\u505a\u5e02API", "\u884c\u60c5API", "\u6301\u4ed3API", "QuantApi", "\u6307\u6807API", "quantapi", "MT4API", "\u57fa\u7840\u5bf9\u8c61\u6570\u636e", "\u914d\u7f6e\u53c2\u6570", "\u516c\u5171\u65b9\u6cd5", "\u65e5\u5fd7API", "\u5feb\u901f\u5f00\u59cb", "\u5b9a\u65f6\u4efb\u52a1", "\u6307\u6807", "\u4ea4\u6613API", "\u4ea4\u6613\u65b9\u6cd5", "\u98ce\u63a7API"], "terms": {"base": 1, "get_bond_cash_flow": [1, 18, 20], "bond_cod": [1, 5, 6], "string": [1, 2, 4, 6, 8, 11, 12, 13, 15, 17, 19, 23], "return": [1, 2, 5, 6, 8, 12, 13, 15, 16, 17, 19, 29], "list": [1, 2, 4, 5, 6, 8, 11, 13, 15, 16, 17, 28], "key": [1, 5, 6, 23], "bondsid": 1, "int": [1, 2, 5, 6, 7, 8, 11, 13, 15, 16, 17, 19, 27, 28, 29, 31], "id": [1, 2, 4, 5, 6, 7, 11, 13, 15, 17, 29], "cashflowno": 1, "currenc": [1, 7, 13], "cashflowtyp": 1, "pemium": 1, "princip": 1, "interest": 1, "fee": 1, "cashflowstatus": 1, "init": [1, 11, 18, 27], "fix": 1, "dealtyp": [1, 5, 6, 12], "pay": 1, "receiv": 1, "cashflowd": 1, "cashflowtim": 1, "amount": [1, 2, 11, 13], "float": [1, 2, 4, 5, 6, 11, 13, 15, 16, 17, 29, 31], "notion": 1, "paymentd": 1, "exdividened": 1, "adjustd": 1, "no": [1, 4, 15], "yes": 1, "startdat": 1, "enddat": 1, "startdateadj": 1, "enddateadj": 1, "period": 1, "phase": 1, "basi": 1, "audittimestamp": 1, "reset": 1, "none": [1, 2, 4, 5, 6, 7, 8, 11, 13, 15, 16, 17, 19, 29, 31], "index": 1, "160017": [1, 6], "1600171": 1, "3700037": 1, "cni": [1, 12], "20230830": 1, "175338": 1, "100": [1, 4, 5, 15, 17, 19, 28, 31], "20160804": 1, "2023": 1, "08": 1, "30": [1, 4, 8, 15, 17, 19, 28], "17": [1, 9], "53": [1, 11], "38": 1, "16001710": 1, "10": [1, 2, 4, 5, 11, 13, 15, 16, 19, 28], "37": 1, "20210204": 1, "20200804": 1, "get_bond_info": [1, 18, 20], "symbol": [1, 2, 4, 5, 6, 7, 8, 11, 12, 13, 15, 16, 17, 19, 29], "str": [1, 2, 5, 6, 7, 8, 11, 13, 16, 17, 19, 23, 27, 28, 29], "dict": [1, 2, 4, 5, 6, 11, 12, 13, 15, 16, 17, 29], "bondcod": 1, "couponfrequ": 1, "1d": [1, 5, 7, 22], "1w": [1, 5, 6, 7, 17, 22], "2w": [1, 5, 17], "1m": [1, 5, 6, 7, 17, 22], "3m": [1, 5, 6], "6m": [1, 5, 6], "1y": [1, 5, 6], "mt": 1, "fixingr": 1, "maturityd": [1, 2, 11, 13], "valued": [1, 2, 11, 13], "160017_t": [1, 4, 5, 6, 11], "get_bull_bear_flag": [1, 18, 20], "name": [1, 8, 11, 19, 23, 27], "bull_bear": [1, 19], "get_contract": [1, 18, 20], "code": [1, 5, 8], "contracttyp": 1, "contracttypeenum": [1, 9], "site": 1, "productbroad": 1, "product": [1, 11], "contractmultipli": 1, "tenor": [1, 5, 17], "tenorgroup": 1, "lastdat": 1, "dealtypegroup": 1, "valuedaterul": 1, "market": [1, 8], "quotecurr": 1, "localnam": [1, 8], "quoteunit": 1, "nodecim": 1, "status": [1, 5, 11, 16], "qlog": [1, 11, 23, 25, 26], "info": [1, 11, 23, 25], "eurusdsp": [1, 5, 6, 8, 11, 15, 16, 17], "boc": 1, "fx": [1, 11], "fxspot": [1, 11, 22], "100000": 1, "spot": [1, 5, 17], "20260701": 1, "comment": [1, 8], "globalid": 1, "quoteunitcodifiersgrpcod": 1, "eurusd": 1, "360t_gtx_qdm": 1, "360t_sst_qdm": 1, "cfets_lc_odm": 1, "cfets_lc_qdm": 1, "fxall_qdm": 1, "jpmc": [1, 22], "ubs": [1, 8, 22], "usd": 1, "timestamp": [1, 8], "2021": [1, 5, 16], "07": 1, "15": [1, 2, 11, 13, 19], "31": 1, "20170701": 1, "con": 1, "query_coupon_pool_bond_info": [1, 18, 20], "coupon_pool_cod": [1, 5, 6], "termtomatur": [1, 5], "deal": [2, 13, 26], "cancel_ord": [2, 13], "orderid": [2, 11, 13], "channel_cod": [2, 5, 7, 12, 13], "side": [2, 5, 7, 11, 12, 13], "default": [2, 4, 5, 6, 7, 11, 13, 15, 16, 17, 19], "sideenum": [2, 9, 11, 13], "216867660628103168": [2, 4, 13, 15], "get_ord": [2, 13], "object": [2, 9, 13, 22, 25, 29], "order": [2, 11, 13], "channelcod": [2, 11, 13], "ordertyp": [2, 11, 13], "ordertypeenum": [2, 9, 11], "sor": [2, 9, 11, 13], "28": [2, 9, 11, 13], "52": [2, 9, 11, 13], "54": [2, 9, 11, 13], "timeinforc": [2, 11, 13], "gtc": [2, 11, 13], "fok": [2, 11, 13], "fak": [2, 11, 13], "gfd": [2, 11, 13], "gtd": [2, 11, 13], "expiretim": [2, 11, 13], "price": [2, 4, 5, 7, 11, 13, 15, 29], "effect": [2, 6, 7, 11, 13, 17], "effectenum": [2, 6, 9, 11, 13, 17], "quantiti": [2, 6, 7, 11, 12, 13, 17, 29], "tradedquant": [2, 11, 13], "orderstatus": [2, 11, 13], "orderstatusenum": [2, 9, 11, 13], "99": [2, 4, 9, 11, 13, 15], "createtim": [2, 11, 13], "inoutmarket": [2, 11, 13], "inoutmarketenum": [2, 9, 11, 13], "errormsg": [2, 11, 13], "tradedavgpric": [2, 11, 13], "tradedavgytm": [2, 11, 13], "hedgeflag": [2, 11, 13], "hedgeflagenum": [2, 9, 11, 13], "intent": [2, 7, 11, 13], "closeorderid": [2, 11, 13], "postyp": [2, 11, 13], "postypeenum": [2, 9, 11, 13], "warnpric": [2, 11, 13], "stoppric": [2, 11, 13], "ctimestamp": [2, 11, 13], "long": [2, 6, 11, 13, 17], "stoplosspric": [2, 11, 13], "takeprofitpric": [2, 11, 13], "closetradedquant": [2, 11, 13], "closeamount": [2, 11, 13], "closetradedavgpric": [2, 11, 13], "closetradedavgytm": [2, 11, 13], "bp": [2, 5, 11, 13], "quoteid": [2, 4, 5, 11, 13, 15], "otheragencyid": [2, 11, 13], "21": [2, 11, 13, 19], "otheragencynam": [2, 11, 13], "othertraderid": [2, 11, 13], "othertradernam": [2, 11, 13], "bpprice": [2, 11, 13], "bondquotetyp": [2, 11, 13], "ytm": [2, 4, 5, 7, 11], "216867535650689024": [2, 13], "swap": [2, 12, 13], "rt_ho": [2, 13], "shibor3m_6m": [2, 12, 13], "50000000": [2, 12, 13], "20240111181652": [2, 13], "1704968212213": [2, 13], "rptype": [2, 11, 13], "order444925856947638272": [2, 13], "to_ord": [2, 5, 13], "order_typ": [2, 13], "in_out_market": [2, 7, 13], "time_in_forc": [2, 7, 13], "expire_tim": [2, 4, 7, 13, 15], "hedge_flag": [2, 7, 13], "warn_pric": [2, 7, 13], "stop_pric": [2, 7, 13], "value_d": [2, 7, 13], "maturity_d": [2, 7, 13], "close_order_id": [2, 7, 13], "pos_typ": [2, 7, 13], "bond_quote_typ": [2, 7, 13], "stop_loss_pric": [2, 13], "take_profit_pric": [2, 13], "doubl": [2, 7, 13], "click": [2, 13], "yyyymmddhhmmss": [2, 4, 11, 13, 15], "hhmmss": [2, 13], "yyyymmdd": [2, 5, 13], "10bp": [2, 13], "context": [2, 11, 13, 29], "ptparam": [2, 13], "data": [2, 5, 7, 11, 13], "bestbid": [2, 11, 13], "95": [2, 13], "if": [2, 11, 13], "els": [2, 13], "bestask": [2, 11, 13], "05": [2, 13], "abs": [2, 13], "sourc": [2, 5, 6, 11, 13, 16, 19, 22, 29], "maker": [4, 11, 15, 26], "to_quot": [4, 15], "maker_quote_depth": [4, 15], "floor_cod": [4, 5, 15, 16], "lp": [4, 15], "vwap": [4, 15], "lc": [4, 15], "bid": [4, 5, 11, 15, 16, 29], "bidamt": [4, 11, 15], "ask": [4, 5, 11, 15, 16, 29], "askamt": [4, 11, 15], "bidytm": 4, "askytm": 4, "bidstrikeyield": 4, "askstrikeyield": 4, "lp_esp1": [4, 11, 15], "cfet": [4, 5, 11, 12, 15, 22], "ind": [4, 11], "bc": [4, 11], "esp": [4, 11], "cfae": [4, 11], "level": [4, 11, 15], "101": 4, "200": [4, 15, 19], "102": 4, "98": 4, "300": [4, 15], "103": 4, "to_quote_cancel": [4, 15], "to_rfq_quot": [4, 11, 15], "reqid": [4, 11, 15], "bidspot": [4, 15], "askspot": [4, 15], "bidpoint": [4, 15], "askpoint": [4, 15], "farbidpoint": [4, 15], "faraskpoint": [4, 15], "strikeyield": [4, 11, 15], "orderqti": [4, 11, 15], "validtim": [4, 11, 15], "taker": [4, 15], "onrfq": [4, 15], "rfqquot": [4, 11, 15], "quotereqid": [4, 15], "rfq": [4, 5, 15], "quotereqld": [4, 15], "to_rfq_quote_cancel": [4, 15], "20": [4, 5, 15, 16, 17, 19, 28], "req_id": [4, 15], "vallduntiltim": [4, 15], "quote_id": [4, 15], "to_rfq_quote_order_confirm": [4, 11, 15], "order_id": [4, 6, 15, 17, 29], "exec_typ": [4, 15], "to_rfq_quote_rej": [4, 15], "quote_req_id": [4, 15], "reject_reason": [4, 15], "reject_text": [4, 15], "match": [4, 15], "for": [4, 15], "inquiri": [4, 15], "inventori": [4, 15], "pass": [4, 11, 15], "11": [4, 8, 11, 15], "md": [5, 16, 26], "cal_bond_yield_curve_ind": 5, "start_dat": 5, "end_dat": 5, "key_tenor": 5, "data_typ": [5, 16], "9m": [5, 6], "2y": [5, 6], "3y": [5, 6], "4y": [5, 6], "5y": [5, 6], "panda": [5, 16, 23], "date": [5, 8, 17, 26], "rate": 5, "datafram": [5, 16], "20240527": 5, "299928": 5, "299547": 5, "20240604": 5, "20240605": 5, "fr007": [5, 6], "20220608": 5, "20220701": 5, "get_active_bond": 5, "virtualcod": 5, "contractcod": 5, "channel": [5, 8], "type": [5, 8, 11, 16], "time": [5, 6, 8, 11, 16, 17, 27], "bond_ho": [5, 6, 7], "100001": 5, "20221001": 5, "20231001": 5, "get_bond_mutual_calcul": 5, "netpric": 5, "fullpric": 5, "discount_factor": 5, "get_bond_residual_curv": 5, "curvetyp": 5, "frequenc": [5, 7, 19], "1n": [5, 7, 22], "num": 5, "start_datatim": [5, 16], "end_datatim": [5, 16], "gb": 5, "gb_hw": 5, "gb_ns": 5, "5n": [5, 7, 22], "15n": [5, 7, 22], "30n": [5, 7, 22], "1h": [5, 7, 22], "numpi": [5, 16, 23], "bar": [5, 16, 19, 22], "curv": 5, "np": [5, 19, 28], "arr": [5, 19], "int64": 5, "price_residu": 5, "float64": [5, 19], "yield_residu": 5, "price_residual_avg": 5, "price_residual_std": 5, "price_residual_var": 5, "yield_residual_avg": 5, "yield_residual_std": 5, "yield_residual_var": 5, "slope": 5, "get_bond_yield_curv": 5, "curve_typ": 5, "bond_typ": 5, "query_typ": 5, "cdb": 5, "eibc": 5, "adbc": 5, "hermit": 5, "hull": 5, "white": 5, "_hw": 5, "gz": 5, "gz_hw": 5, "valu": [5, 6, 17], "fwd": 5, "matur": 5, "gk": 5, "09": [5, 8], "get_bond_yield_curve_slop": 5, "key_tenor_a": 5, "key_tenor_b": 5, "get_cn_bond_evalu": 5, "bond_nam": 5, "bond_type_nam": 5, "valuation_d": 5, "accrued_interest": [5, 6], "durat": [5, 6], "convex": [5, 6], "get_depth_info_quoteid": 5, "bondsdeal": 5, "qdm_ho": 5, "get_irs_df": 5, "date_list": 5, "20240608": [5, 11], "9696126290413021": 5, "20240701": 5, "9682810793669262": 5, "1672988400000": 5, "9796126290413021": 5, "9782810793669262": 5, "1672988900000": 5, "get_irs_fixing_curv": 5, "mdtype": 5, "shibor": 5, "securitytyp": 5, "shiborcn": 5, "fr001": 5, "fr014": 5, "shiborbp": 5, "benchmarkeffectived": 5, "get_pric": [5, 16], "type_": [5, 16], "field": [5, 16, 19], "quotestatusenum": [5, 9, 16], "best_bid": [5, 7, 16], "best_bid_amt": [5, 7, 16], "best_ask": [5, 16], "best_ask_amt": [5, 16], "ask_vol": [5, 11, 16], "bid_vol": [5, 11, 16], "limit_up": [5, 16], "limit_down": [5, 16], "cfets_lc": [5, 11, 16], "odm_depth": [5, 11, 16], "1598922000100": [5, 11, 16], "19907": [5, 16], "94": [5, 16], "19919": [5, 16], "19925": [5, 16], "19931": [5, 16], "19937": [5, 16], "19943": [5, 16], "19901": [5, 16], "19895": [5, 16], "19889": [5, 16], "19883": [5, 16], "get_xswap_curv": 5, "curvetenor": 5, "ratecurv": 5, "curved": 5, "spotratecurv": 5, "positivespotratecurv": 5, "negativespotratecurv": 5, "inactivebondpr": 5, "dirtypric": 5, "yieldtomatur": 5, "put_maker_symbol": [5, 16], "symbol_list": [5, 16], "query_bar_spread": 5, "spread_contract_nam": 5, "count": [5, 16], "df": [5, 16], "fals": [5, 16, 31], "ndarray": [5, 28], "ani": 5, "dtype": 5, "_scalartype_co": 5, "spread_contract_ind": 5, "2h": [5, 7], "4h": [5, 7], "bool": [5, 16, 29, 31], "nparr": 5, "open": [5, 16, 19], "close": [5, 16, 19, 28], "high": [5, 16, 28], "low": [5, 16, 28], "strat": [5, 16], "end": [5, 16], "barnp": 5, "query_bar": [5, 16], "5n_bar_odm_depth": [5, 16], "1532017800200": [5, 16], "start": [5, 16], "1532018100000": [5, 16], "1532018400000": [5, 16], "1648800000000001": [5, 16], "16487": [5, 16], "164864**********": [5, 16], "query_bars_pro": [5, 16], "from": [5, 16, 26], "quantapi": [5, 16, 26], "enum": [5, 16], "import": [5, 16, 26], "data_type_panda": [5, 16], "1n_bar_depth": 5, "12": [5, 16, 19, 28], "00": [5, 8, 16], "2024": 5, "query_coupons_pool_bar": 5, "start_termtomatur": [5, 6], "end_termtomatur": [5, 6], "20200101": 5, "20240101": 5, "query_spread": 5, "start_tim": 5, "end_tim": 5, "tick": [5, 11], "arrnp": 5, "1611681075394": 5, "1711681075394": 5, "pos": [6, 17, 26], "get_bond_dimension_loss_profit": 6, "dimens": 6, "liquid": 6, "bonds_classif": 6, "cost_pric": 6, "trade_interest": 6, "pay_date_incom": 6, "capital_gain": 6, "loan_fe": 6, "float_pl": 6, "profit": [6, 17], "get_bond_loss_profit": 6, "unrealizedpl": [6, 12, 17], "realizedpl": [6, 12, 17], "get_dv01": 6, "dv01": [6, 17], "unit_dv01": 6, "tactics_sublevel_dv01_and_all_dv01": 6, "index_": 6, "_sublevel_dv01_and_all_dv01": 6, "000128515849878681": 6, "000511175662487819": 6, "001504189594754379": 6, "003150001287815192": 6, "004549469498652091": 6, "006196887571089486": 6, "011579257279061596": 6, "017673877414448803": 6, "02495314119810738": 6, "030532689279085506": 6, "all": [6, 12], "944": 6, "4172199859313": 6, "get_ind": 6, "or": [6, 8], "mod_dur": 6, "spread_dur": 6, "spread_convex": 6, "ir_dur": 6, "ir_convex": 6, "160016_t": 6, "355": 6, "6933": 6, "35443": 6, "get_ord_posit": [6, 17], "posit": [6, 17], "frozenquant": [6, 17], "quantitytd": [6, 17], "possid": [6, 17], "possideenum": [6, 9, 17], "costpric": [6, 17], "washamount": [6, 17], "216868121676222464": [6, 17], "2145": [6, 17], "0000000000637": [6, 17], "16064": [6, 17], "1530524400101": [6, 17], "get_posit": [6, 17], "pos_sid": [6, 17], "get_position_onroad": [6, 17], "onroad_b": [6, 17], "onroad_": [6, 17], "60": [6, 17, 19], "50": [6, 11, 13, 17, 19, 28], "1000": [6, 17], "get_xswap_loss_profit": 6, "shibor3m": 6, "query_coupon_pool_ind": 6, "dv01_unit": 6, "signal": [7, 26], "to_sign": 7, "descript": 7, "bond": 7, "get_bus_day": [8, 20], "20190121": 8, "get_bus_tim": [8, 20], "day": 8, "am": 8, "pm": 8, "vesp": 8, "starttim": 8, "endtim": 8, "adjusttyp": 8, "marketstatus": 8, "oc": 8, "pr": 8, "tradeperiodgroup": 8, "tradeperiodtim": 8, "ubs_ho": [8, 11, 16, 22], "get_open_close_market": [8, 20], "query_tim": 8, "20221201": 8, "get_sys_tim": [8, 20], "20190128121103": 8, "get_timestamp": [8, 20], "1705632663000": 8, "class": [9, 11, 22], "enum_erayt": 9, "contractstatusenum": 9, "22": 9, "oco": 9, "if_don": 9, "18": [9, 23], "if_done_oco": 9, "19": 9, "event": 11, "onbusinessd": [11, 18], "senddat": 11, "20230530": 11, "sendtim": 11, "163000": 11, "instrument": 11, "befored": 11, "20230529": 11, "afterd": 11, "nowtim": 11, "def": 11, "ondata": [11, 18], "subscrib": 11, "1532000820300": 11, "16478": 11, "bestbidamt": 11, "57": 11, "1649": 11, "bestaskamt": 11, "16496": 11, "16502": 11, "16507": 11, "16513": 11, "16472": 11, "16466": 11, "16461": 11, "16455": 11, "limitup": 11, "limitdown": 11, "onord": [11, 18], "200000": 11, "20240111152407": 11, "1704957847416": 11, "seq": 11, "symbolcnam": 11, "order444882385620705280": 11, "onquot": [11, 18], "quot": 11, "b51ed597": 11, "9f7f": 11, "3fb4": 11, "b8c9": 11, "fd77e1aab906": 11, "1705043184689": 11, "floorcod": 11, "quotetypestr": 11, "quotedatetim": 11, "20240112150624000": 11, "makerdepth": 11, "to_quto": 11, "positionno": 11, "1000000": 11, "1951": 11, "1959": 11, "errortext": 11, "883d7924": 11, "8ef0": 11, "315a": 11, "93d7": 11, "7c736d46c15d": 11, "119": 11, "9371": 11, "10000000": 11, "9805": 11, "bidyield": 11, "askyield": 11, "20240112145715": 11, "to_quote_order_confirm": [11, 15], "onquoteord": [11, 18], "quoteord": 11, "223483495955697664": 11, "196000000": 11, "tradequant": 11, "18294": 11, "farpric": 11, "spotrat": 11, "point": [11, 29], "farpoint": 11, "1625198940000": 11, "partyid": 11, "yt": 11, "partyroleid": 11, "onrfqquot": [11, 18], "lp_ind1": 11, "216869925133619200": 11, "cfets_lp": 11, "usdcnhsp": 11, "98749": 11, "98784": 11, "bidspotr": 11, "askspotr": 11, "bidfwdpoint": 11, "askfwdpoint": 11, "farbid": 11, "farask": 11, "farbidfwdpoint": 11, "faraskfwdpoint": 11, "yield": 11, "onrfqquoteord": [11, 18], "rfqquoteord": 11, "216870196848627712": 11, "51": 11, "1532043000101": 11, "trade_id": 11, "onrfqreq": [11, 18], "rfqreq": 11, "quotes": 11, "quote_status": 11, "click_status": 11, "info_f": [11, 25], "ontim": [11, 18, 27], "schedul": [11, 26, 27], "run_daili": [11, 27], "my_job": 11, "160000": [11, 27], "ontrad": [11, 18], "trade": 11, "20180722": 11, "1657599999999997": 11, "657599999999997": 11, "tradetim": 11, "20180720073000": 11, "nearpric": 11, "343": 11, "443": 11, "neardat": 11, "20240601": 11, "fardat": 11, "1165759": 11, "**********": 11, "fund": 12, "get_fund": 12, "net": 12, "freeze_amt": 12, "commiss": 12, "cci": [12, 18, 28], "init_money": 12, "net_pre_freez": 12, "amt": [12, 17], "cost": 12, "oc_pre_freez": 12, "net_freez": 12, "oc_freez": 12, "1500000": 12, "20000000": 12, "equiti": 12, "65353": 12, "get_market_info": 12, "market_cod": 12, "rt": 12, "contract_cod": 12, "tradetimecod": 12, "dealholidayid": 12, "settleholidayid": 12, "minpricechang": 12, "mindealamount": 12, "maxdealamount": 12, "priceuplimit": 12, "pricedownlimit": 12, "speculationbuydepositr": 12, "speculationselldepositr": 12, "hedgingbuydepositr": 12, "hedgingselldepositr": 12, "mindealamountchangeunit": 12, "commissiontyp": 12, "opencast": 12, "flatthisunwindcast": 12, "flatyesterdaycast": 12, "quotedvaliddigit": 12, "cost_curr": 12, "1958813630450311": 15, "1972186369549689": 15, "yieldrat": 15, "1d_bar_depth": 16, "get_fx_exposur": 17, "pair": 17, "usdjpi": 17, "total": 17, "fv": 17, "npv": 17, "20240301": 17, "record": 17, "20240302": 17, "usdcni": 17, "balanc": 17, "tom": 17, "3w": 17, "get_fxpm_exposur": 17, "xauusd": 17, "un_deliv": 17, "20241010": 17, "20241011": 17, "deliv": 17, "frozenquantitytd": 17, "frozenquantityyestd": 17, "api": [18, 20, 26], "kelchan": [18, 28], "ma": [18, 28], "macd": [18, 19, 28], "mfi": [18, 28], "mom": [18, 28], "obv": [18, 28], "rsi": [18, 28], "stoch": [18, 28], "indicator_arsen": [19, 26], "eraytma": 19, "bar_count": 19, "ema20": 19, "ema30": 19, "ema40": 19, "40": 19, "ema50": 19, "ema60": 19, "fibonaccima": 19, "sma5": 19, "sma8": 19, "sma13": 19, "13": 19, "sma21": 19, "sma34": 19, "34": 19, "sma55": 19, "55": 19, "sma89": 19, "89": 19, "sma144": 19, "144": 19, "pp_woodi": 19, "gmma": 19, "ema3": 19, "ema5": 19, "ema8": 19, "ema10": 19, "ema12": 19, "ema15": 19, "ema35": 19, "35": 19, "ema45": 19, "45": 19, "gmmas": 19, "macdbol": 19, "boll_timeperiod": 19, "boll_nbdevup": 19, "boll_nbdevdn": 19, "macd_fastperiod": 19, "macd_slowperiod": 19, "macd_signalperiod": 19, "dea": 19, "mark": 19, "pp_camarilla": 19, "r5": 19, "r4": 19, "r3": 19, "r2": 19, "r1": 19, "s1": 19, "s2": 19, "s3": 19, "s4": 19, "s5": 19, "pivot_point": 19, "pp_demark": 19, "pp_fibonacci": 19, "pp": 19, "pp_standard": 19, "tradma": 19, "sma10": 19, "sma20": 19, "sma30": 19, "sma60": 19, "bardatalist": 22, "_bar_depth": 22, "jpmc_ho": 22, "odm": 22, "odm_ho": 22, "qdm": 22, "qdm_full_ho": 22, "qdm_sweep_ho": 22, "marketdatalist": 22, "depthquot": 22, "param": [23, 26], "get": 23, "paramkey": 23, "tab": 23, "will": 23, "age": 23, "get_group_nam": 23, "get_matrix_np": 23, "sheet_nam": 23, "excel": 23, "sheet": 23, "get_matrix_pb": 23, "error": 25, "nformat": 25, "error_f": 25, "arg": 25, "version": 25, "eg": 26, "as": 26, "trade_arsen": [26, 29], "equantapi": [26, 31], "xrisk": [26, 31], "mt4": 26, "ordersend": 26, "orderclos": 26, "ordermodifi": 26, "trigger": 27, "16": 27, "run_second": 27, "5s": 27, "talib_erayt": 28, "timeperiod": 28, "14": 28, "talib": 28, "array": 28, "nan": 28, "111": 28, "11111111": 28, "cal_factor": 28, "ma_typ": 28, "ema": 28, "quanterrorexcept": [28, 31], "se": 28, "fastperiod": 28, "slowperiod": 28, "26": 28, "signalperiod": 28, "macdsign": 28, "macdhist": 28, "volum": 28, "70": 28, "90": 28, "120": 28, "140": 28, "160": 28, "190": 28, "210": 28, "240": 28, "fastk_period": 28, "slowk_period": 28, "slowk_matyp": 28, "slowd_period": 28, "slowd_matyp": 28, "kd": 28, "check_spread": 29, "point_limit": 29, "flag": 29, "close_al": 29, "keep_sid": 29, "slippag": 29, "digit": 29, "get_count_ord": 29, "keyword": 29, "argument": 29, "buy_num": 29, "sell_num": 29, "buy_lots_al": 29, "sell_lots_al": 29, "buy_lots_on": 29, "sell_lots_on": 29, "buy_prof": 29, "sell_prof": 29, "buy_avg": 29, "sell_avg": 29, "buy_pric": 29, "sell_pric": 29, "buy_id": 29, "sell_id": 29, "count_ord": 29, "init_count_ord": 29, "open_buy": 29, "open_sel": 29, "risk_param_init": 31, "max_loss": 31, "today_max_loss": 31, "today_max_ord": 31, "today_undo_max_ord": 31, "than_max_loss": 31, "true": 31, "than_today_max_loss": 31, "than_today_max_ord": 31, "than_today_max_undo_ord": 31}, "objects": {"": [[1, 0, 0, "-", "base"], [8, 0, 0, "-", "date"], [9, 0, 0, "-", "enum_erayt"], [11, 0, 0, "-", "event"], [12, 0, 0, "-", "funds"], [19, 0, 0, "-", "indicator_arsenals"], [22, 0, 0, "-", "object"], [23, 0, 0, "-", "param"], [25, 0, 0, "-", "qlog"], [26, 0, 0, "-", "quick_start"], [27, 0, 0, "-", "scheduler"], [28, 0, 0, "-", "talib_erayt"], [29, 0, 0, "-", "trade_arsenals"]], "base": [[1, 1, 1, "", "get_bond_cash_flow"], [1, 1, 1, "", "get_bond_info"], [1, 1, 1, "", "get_bull_bear_flag"], [1, 1, 1, "", "get_contract"], [1, 1, 1, "", "query_coupon_pool_bond_info"]], "date": [[8, 1, 1, "", "get_bus_day"], [8, 1, 1, "", "get_bus_time"], [8, 1, 1, "", "get_open_close_market"], [8, 1, 1, "", "get_sys_time"], [8, 1, 1, "", "get_timestamp"]], "enum_erayt": [[9, 2, 1, "", "ContractStatusEnum"], [9, 2, 1, "", "ContractTypeEnum"], [9, 2, 1, "", "EffectEnum"], [9, 2, 1, "", "HedgeFlagEnum"], [9, 2, 1, "", "InOutMarketEnum"], [9, 2, 1, "", "OrderStatusEnum"], [9, 2, 1, "", "OrderTypeEnum"], [9, 2, 1, "", "PosSideEnum"], [9, 2, 1, "", "PosTypeEnum"], [9, 2, 1, "", "QuoteStatusEnum"], [9, 2, 1, "", "SideEnum"]], "equantapi": [[31, 0, 0, "-", "xrisk"]], "equantapi.xrisk": [[31, 1, 1, "", "risk_param_init"], [31, 1, 1, "", "than_max_loss"], [31, 1, 1, "", "than_today_max_loss"], [31, 1, 1, "", "than_today_max_orders"], [31, 1, 1, "", "than_today_max_undo_orders"]], "event": [[11, 1, 1, "", "init"], [11, 1, 1, "", "onBusinessDate"], [11, 1, 1, "", "onData"], [11, 1, 1, "", "onOrder"], [11, 1, 1, "", "onQuote"], [11, 1, 1, "", "onQuoteOrder"], [11, 1, 1, "", "onRfqQuote"], [11, 1, 1, "", "onRfqQuoteOrder"], [11, 1, 1, "", "onRfqReq"], [11, 1, 1, "", "onTime"], [11, 1, 1, "", "onTrade"]], "funds": [[12, 1, 1, "", "get_funds"], [12, 1, 1, "", "get_market_info"]], "indicator_arsenals": [[19, 1, 1, "", "BULL_BEAR"], [19, 1, 1, "", "ERAYTMA"], [19, 1, 1, "", "FibonacciMA"], [19, 1, 1, "", "GMMA"], [19, 1, 1, "", "MACDBOLL"], [19, 1, 1, "", "PP_Camarilla"], [19, 1, 1, "", "PP_DeMarks"], [19, 1, 1, "", "PP_Fibonacci"], [19, 1, 1, "", "PP_Standard"], [19, 1, 1, "", "PP_Woodie"], [19, 1, 1, "", "TRADMA"]], "object": [[22, 2, 1, "", "BarDataList"], [22, 2, 1, "", "MarketDataList"]], "param": [[23, 1, 1, "", "get"], [23, 1, 1, "", "get_group_names"], [23, 1, 1, "", "get_matrix_np"], [23, 1, 1, "", "get_matrix_pb"]], "qlog": [[25, 1, 1, "", "error"], [25, 1, 1, "", "error_f"], [25, 1, 1, "", "info"], [25, 1, 1, "", "info_f"]], "scheduler": [[27, 1, 1, "", "run_daily"], [27, 1, 1, "", "run_second"]], "talib_erayt": [[28, 1, 1, "", "CCI"], [28, 1, 1, "", "KELCHAN"], [28, 1, 1, "", "MA"], [28, 1, 1, "", "MACD"], [28, 1, 1, "", "MFI"], [28, 1, 1, "", "MOM"], [28, 1, 1, "", "OBV"], [28, 1, 1, "", "RSI"], [28, 1, 1, "", "STOCH"]], "trade_arsenals": [[29, 1, 1, "", "check_spread"], [29, 1, 1, "", "close_all"], [29, 1, 1, "", "get_count_orders"], [29, 1, 1, "", "init_count_orders"], [29, 1, 1, "", "open_buy"], [29, 1, 1, "", "open_sell"]], "\u56fa\u6536": [[2, 0, 0, "module-.deal", "deal"], [4, 0, 0, "module-.maker", "maker"], [5, 0, 0, "module-.md", "md"], [6, 0, 0, "module-.pos", "pos"], [7, 0, 0, "module-.signal", "signal"]], "\u56fa\u6536.deal": [[2, 1, 1, "deal.cancel_order", "cancel_order"], [2, 1, 1, "deal.get_order", "get_order"], [2, 1, 1, "deal.get_orders", "get_orders"], [2, 1, 1, "deal.to_order", "to_order"]], "\u56fa\u6536.maker": [[4, 1, 1, "maker.to_quote", "to_quote"], [4, 1, 1, "maker.to_quote_cancel", "to_quote_cancel"], [4, 1, 1, "maker.to_rfq_quote", "to_rfq_quote"], [4, 1, 1, "maker.to_rfq_quote_cancel", "to_rfq_quote_cancel"], [4, 1, 1, "maker.to_rfq_quote_order_confirm", "to_rfq_quote_order_confirm"], [4, 1, 1, "maker.to_rfq_quote_rej", "to_rfq_quote_rej"]], "\u56fa\u6536.md": [[5, 1, 1, "md.cal_bond_yield_curve_indicator", "cal_bond_yield_curve_indicator"], [5, 1, 1, "md.get_active_bond", "get_active_bond"], [5, 1, 1, "md.get_bond_mutual_calculation", "get_bond_mutual_calculation"], [5, 1, 1, "md.get_bond_residual_curve", "get_bond_residual_curve"], [5, 1, 1, "md.get_bond_yield_curve", "get_bond_yield_curve"], [5, 1, 1, "md.get_bond_yield_curve_slope", "get_bond_yield_curve_slope"], [5, 1, 1, "md.get_cn_bond_evaluation", "get_cn_bond_evaluation"], [5, 1, 1, "md.get_depth_info_quoteid", "get_depth_info_quoteid"], [5, 1, 1, "md.get_irs_df", "get_irs_df"], [5, 1, 1, "md.get_irs_fixing_curve", "get_irs_fixing_curve"], [5, 1, 1, "md.get_price", "get_price"], [5, 1, 1, "md.get_xswap_curve", "get_xswap_curve"], [5, 1, 1, "md.inactiveBondPricing", "inactiveBondPricing"], [5, 1, 1, "md.put_maker_symbol", "put_maker_symbol"], [5, 1, 1, "md.query_bar_spreads", "query_bar_spreads"], [5, 1, 1, "md.query_bars", "query_bars"], [5, 1, 1, "md.query_bars_pro", "query_bars_pro"], [5, 1, 1, "md.query_coupons_pool_bars", "query_coupons_pool_bars"], [5, 1, 1, "md.query_spreads", "query_spreads"]], "\u56fa\u6536.pos": [[6, 1, 1, "pos.get_bond_dimension_loss_profit", "get_bond_dimension_loss_profit"], [6, 1, 1, "pos.get_bond_loss_profit", "get_bond_loss_profit"], [6, 1, 1, "pos.get_dv01", "get_dv01"], [6, 1, 1, "pos.get_indicators", "get_indicators"], [6, 1, 1, "pos.get_ord_position", "get_ord_position"], [6, 1, 1, "pos.get_position", "get_position"], [6, 1, 1, "pos.get_position_onroad", "get_position_onroad"], [6, 1, 1, "pos.get_xswap_loss_profit", "get_xswap_loss_profit"], [6, 1, 1, "pos.query_coupon_pool_indicators", "query_coupon_pool_indicators"]], "\u56fa\u6536.signal": [[7, 1, 1, "signal.to_signal", "to_signal"]], "\u5916\u6c47": [[13, 0, 0, "module-.deal", "deal"], [15, 0, 0, "module-.maker", "maker"], [16, 0, 0, "module-.md", "md"], [17, 0, 0, "module-.pos", "pos"]], "\u5916\u6c47.deal": [[13, 1, 1, "deal.cancel_order", "cancel_order"], [13, 1, 1, "deal.get_order", "get_order"], [13, 1, 1, "deal.get_orders", "get_orders"], [13, 1, 1, "deal.to_order", "to_order"]], "\u5916\u6c47.maker": [[15, 1, 1, "maker.to_quote", "to_quote"], [15, 1, 1, "maker.to_quote_cancel", "to_quote_cancel"], [15, 1, 1, "maker.to_quote_order_confirm", "to_quote_order_confirm"], [15, 1, 1, "maker.to_rfq_quote", "to_rfq_quote"], [15, 1, 1, "maker.to_rfq_quote_cancel", "to_rfq_quote_cancel"], [15, 1, 1, "maker.to_rfq_quote_order_confirm", "to_rfq_quote_order_confirm"], [15, 1, 1, "maker.to_rfq_quote_rej", "to_rfq_quote_rej"]], "\u5916\u6c47.md": [[16, 1, 1, "md.get_price", "get_price"], [16, 1, 1, "md.put_maker_symbol", "put_maker_symbol"], [16, 1, 1, "md.query_bars", "query_bars"], [16, 1, 1, "md.query_bars_pro", "query_bars_pro"]], "\u5916\u6c47.pos": [[17, 1, 1, "pos.get_fx_exposure", "get_fx_exposure"], [17, 1, 1, "pos.get_fxpm_exposure", "get_fxpm_exposure"], [17, 1, 1, "pos.get_ord_position", "get_ord_position"], [17, 1, 1, "pos.get_position", "get_position"], [17, 1, 1, "pos.get_position_onroad", "get_position_onroad"]]}, "objtypes": {"0": "py:module", "1": "py:function", "2": "py:class"}, "objnames": {"0": ["py", "module", "Python \u6a21\u5757"], "1": ["py", "function", "Python \u51fd\u6570"], "2": ["py", "class", "Python \u7c7b"]}, "titleterms": {"api": [1, 2, 3, 4, 5, 6, 7, 8, 13, 14, 15, 16, 17, 19, 25, 29, 31], "quantapi": [18, 20], "mt4api": 21}, "envversion": {"sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx": 60}, "alltitles": {"\u6b66\u5668\u5e93": [[0, "id1"]], "\u57fa\u7840API": [[1, "module-base"]], "\u8ba2\u5355API": [[2, "module-.deal"], [13, "module-.deal"]], "\u56fa\u6536API": [[3, "api"]], "\u505a\u5e02API": [[4, "module-.maker"], [15, "module-.maker"]], "\u884c\u60c5API": [[5, "module-.md"], [16, "module-.md"]], "\u6301\u4ed3API": [[6, "module-.pos"], [17, "module-.pos"]], "\u4fe1\u53f7API": [[7, "module-.signal"]], "\u65f6\u95f4API": [[8, "module-date"]], "\u5e38\u7528\u679a\u4e3e": [[9, "module-enum_erayt"]], "\u5e38\u7528\u679a\u4e3e\u548c\u7ed3\u6784": [[10, "id1"]], "\u4ea4\u6613\u4e8b\u4ef6": [[11, "module-event"]], "\u8d44\u91d1\u8d26\u53f7": [[12, "module-funds"]], "\u5916\u6c47API": [[14, "api"]], "QuantApi": [[18, "quantapi"]], "\u6307\u6807API": [[19, "module-indicator_arsenals"]], "quantapi": [[20, "quantapi"]], "MT4API": [[21, "mt4api"]], "\u57fa\u7840\u5bf9\u8c61\u6570\u636e": [[22, "module-object"]], "\u914d\u7f6e\u53c2\u6570": [[23, "module-param"]], "\u516c\u5171\u65b9\u6cd5": [[24, "id1"]], "\u65e5\u5fd7API": [[25, "module-qlog"]], "\u5feb\u901f\u5f00\u59cb": [[26, "module-quick_start"]], "\u5b9a\u65f6\u4efb\u52a1": [[27, "module-scheduler"]], "\u6307\u6807": [[28, "module-talib_erayt"]], "\u4ea4\u6613API": [[29, "module-trade_arsenals"]], "\u4ea4\u6613\u65b9\u6cd5": [[30, "id1"]], "\u98ce\u63a7API": [[31, "module-equantapi.xrisk"]]}, "indexentries": {"base": [[1, "module-base"]], "get_bond_cash_flow()\uff08\u5728 base \u6a21\u5757\u4e2d\uff09": [[1, "base.get_bond_cash_flow"]], "get_bond_info()\uff08\u5728 base \u6a21\u5757\u4e2d\uff09": [[1, "base.get_bond_info"]], "get_bull_bear_flag()\uff08\u5728 base \u6a21\u5757\u4e2d\uff09": [[1, "base.get_bull_bear_flag"]], "get_contract()\uff08\u5728 base \u6a21\u5757\u4e2d\uff09": [[1, "base.get_contract"]], "module": [[1, "module-base"], [2, "module-.deal"], [4, "module-.maker"], [5, "module-.md"], [6, "module-.pos"], [7, "module-.signal"], [8, "module-date"], [9, "module-enum_erayt"], [11, "module-event"], [12, "module-funds"], [13, "module-.deal"], [15, "module-.maker"], [16, "module-.md"], [17, "module-.pos"], [19, "module-indicator_arsenals"], [22, "module-object"], [23, "module-param"], [25, "module-qlog"], [26, "module-quick_start"], [27, "module-scheduler"], [28, "module-talib_erayt"], [29, "module-trade_arsenals"], [31, "module-equantapi.xrisk"]], "query_coupon_pool_bond_info()\uff08\u5728 base \u6a21\u5757\u4e2d\uff09": [[1, "base.query_coupon_pool_bond_info"]], "cancel_order()\uff08\u5728 \u56fa\u6536.deal \u6a21\u5757\u4e2d\uff09": [[2, "deal.cancel_order"]], "get_order()\uff08\u5728 \u56fa\u6536.deal \u6a21\u5757\u4e2d\uff09": [[2, "deal.get_order"]], "get_orders()\uff08\u5728 \u56fa\u6536.deal \u6a21\u5757\u4e2d\uff09": [[2, "deal.get_orders"]], "to_order()\uff08\u5728 \u56fa\u6536.deal \u6a21\u5757\u4e2d\uff09": [[2, "deal.to_order"]], "\u56fa\u6536.deal": [[2, "module-.deal"]], "to_quote()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_quote"]], "to_quote_cancel()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_quote_cancel"]], "to_rfq_quote()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_rfq_quote"]], "to_rfq_quote_cancel()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_rfq_quote_cancel"]], "to_rfq_quote_order_confirm()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_rfq_quote_order_confirm"]], "to_rfq_quote_rej()\uff08\u5728 \u56fa\u6536.maker \u6a21\u5757\u4e2d\uff09": [[4, "maker.to_rfq_quote_rej"]], "\u56fa\u6536.maker": [[4, "module-.maker"]], "cal_bond_yield_curve_indicator()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.cal_bond_yield_curve_indicator"]], "get_active_bond()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_active_bond"]], "get_bond_mutual_calculation()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_bond_mutual_calculation"]], "get_bond_residual_curve()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_bond_residual_curve"]], "get_bond_yield_curve()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_bond_yield_curve"]], "get_bond_yield_curve_slope()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_bond_yield_curve_slope"]], "get_cn_bond_evaluation()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_cn_bond_evaluation"]], "get_depth_info_quoteid()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_depth_info_quoteid"]], "get_irs_df()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_irs_df"]], "get_irs_fixing_curve()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_irs_fixing_curve"]], "get_price()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_price"]], "get_xswap_curve()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.get_xswap_curve"]], "inactivebondpricing()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.inactiveBondPricing"]], "put_maker_symbol()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.put_maker_symbol"]], "query_bar_spreads()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.query_bar_spreads"]], "query_bars()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.query_bars"]], "query_bars_pro()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.query_bars_pro"]], "query_coupons_pool_bars()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.query_coupons_pool_bars"]], "query_spreads()\uff08\u5728 \u56fa\u6536.md \u6a21\u5757\u4e2d\uff09": [[5, "md.query_spreads"]], "\u56fa\u6536.md": [[5, "module-.md"]], "get_bond_dimension_loss_profit()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_bond_dimension_loss_profit"]], "get_bond_loss_profit()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_bond_loss_profit"]], "get_dv01()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_dv01"]], "get_indicators()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_indicators"]], "get_ord_position()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_ord_position"]], "get_position()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_position"]], "get_position_onroad()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_position_onroad"]], "get_xswap_loss_profit()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.get_xswap_loss_profit"]], "query_coupon_pool_indicators()\uff08\u5728 \u56fa\u6536.pos \u6a21\u5757\u4e2d\uff09": [[6, "pos.query_coupon_pool_indicators"]], "\u56fa\u6536.pos": [[6, "module-.pos"]], "to_signal()\uff08\u5728 \u56fa\u6536.signal \u6a21\u5757\u4e2d\uff09": [[7, "signal.to_signal"]], "\u56fa\u6536.signal": [[7, "module-.signal"]], "date": [[8, "module-date"]], "get_bus_day()\uff08\u5728 date \u6a21\u5757\u4e2d\uff09": [[8, "date.get_bus_day"]], "get_bus_time()\uff08\u5728 date \u6a21\u5757\u4e2d\uff09": [[8, "date.get_bus_time"]], "get_open_close_market()\uff08\u5728 date \u6a21\u5757\u4e2d\uff09": [[8, "date.get_open_close_market"]], "get_sys_time()\uff08\u5728 date \u6a21\u5757\u4e2d\uff09": [[8, "date.get_sys_time"]], "get_timestamp()\uff08\u5728 date \u6a21\u5757\u4e2d\uff09": [[8, "date.get_timestamp"]], "contractstatusenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.ContractStatusEnum"]], "contracttypeenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.ContractTypeEnum"]], "effectenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.EffectEnum"]], "hedgeflagenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.HedgeFlagEnum"]], "inoutmarketenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.InOutMarketEnum"]], "orderstatusenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.OrderStatusEnum"]], "ordertypeenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.OrderTypeEnum"]], "possideenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.PosSideEnum"]], "postypeenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.PosTypeEnum"]], "quotestatusenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.QuoteStatusEnum"]], "sideenum\uff08enum_erayt \u4e2d\u7684\u7c7b\uff09": [[9, "enum_erayt.SideEnum"]], "enum_erayt": [[9, "module-enum_erayt"]], "event": [[11, "module-event"]], "init()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.init"]], "onbusinessdate()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onBusinessDate"]], "ondata()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onData"]], "onorder()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onOrder"]], "onquote()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onQuote"]], "onquoteorder()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onQuoteOrder"]], "onrfqquote()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onRfqQuote"]], "onrfqquoteorder()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onRfqQuoteOrder"]], "onrfqreq()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onRfqReq"]], "ontime()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onTime"]], "ontrade()\uff08\u5728 event \u6a21\u5757\u4e2d\uff09": [[11, "event.onTrade"]], "funds": [[12, "module-funds"]], "get_funds()\uff08\u5728 funds \u6a21\u5757\u4e2d\uff09": [[12, "funds.get_funds"]], "get_market_info()\uff08\u5728 funds \u6a21\u5757\u4e2d\uff09": [[12, "funds.get_market_info"]], "cancel_order()\uff08\u5728 \u5916\u6c47.deal \u6a21\u5757\u4e2d\uff09": [[13, "deal.cancel_order"]], "get_order()\uff08\u5728 \u5916\u6c47.deal \u6a21\u5757\u4e2d\uff09": [[13, "deal.get_order"]], "get_orders()\uff08\u5728 \u5916\u6c47.deal \u6a21\u5757\u4e2d\uff09": [[13, "deal.get_orders"]], "to_order()\uff08\u5728 \u5916\u6c47.deal \u6a21\u5757\u4e2d\uff09": [[13, "deal.to_order"]], "\u5916\u6c47.deal": [[13, "module-.deal"]], "to_quote()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_quote"]], "to_quote_cancel()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_quote_cancel"]], "to_quote_order_confirm()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_quote_order_confirm"]], "to_rfq_quote()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_rfq_quote"]], "to_rfq_quote_cancel()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_rfq_quote_cancel"]], "to_rfq_quote_order_confirm()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_rfq_quote_order_confirm"]], "to_rfq_quote_rej()\uff08\u5728 \u5916\u6c47.maker \u6a21\u5757\u4e2d\uff09": [[15, "maker.to_rfq_quote_rej"]], "\u5916\u6c47.maker": [[15, "module-.maker"]], "get_price()\uff08\u5728 \u5916\u6c47.md \u6a21\u5757\u4e2d\uff09": [[16, "md.get_price"]], "put_maker_symbol()\uff08\u5728 \u5916\u6c47.md \u6a21\u5757\u4e2d\uff09": [[16, "md.put_maker_symbol"]], "query_bars()\uff08\u5728 \u5916\u6c47.md \u6a21\u5757\u4e2d\uff09": [[16, "md.query_bars"]], "query_bars_pro()\uff08\u5728 \u5916\u6c47.md \u6a21\u5757\u4e2d\uff09": [[16, "md.query_bars_pro"]], "\u5916\u6c47.md": [[16, "module-.md"]], "get_fx_exposure()\uff08\u5728 \u5916\u6c47.pos \u6a21\u5757\u4e2d\uff09": [[17, "pos.get_fx_exposure"]], "get_fxpm_exposure()\uff08\u5728 \u5916\u6c47.pos \u6a21\u5757\u4e2d\uff09": [[17, "pos.get_fxpm_exposure"]], "get_ord_position()\uff08\u5728 \u5916\u6c47.pos \u6a21\u5757\u4e2d\uff09": [[17, "pos.get_ord_position"]], "get_position()\uff08\u5728 \u5916\u6c47.pos \u6a21\u5757\u4e2d\uff09": [[17, "pos.get_position"]], "get_position_onroad()\uff08\u5728 \u5916\u6c47.pos \u6a21\u5757\u4e2d\uff09": [[17, "pos.get_position_onroad"]], "\u5916\u6c47.pos": [[17, "module-.pos"]], "bull_bear()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.BULL_BEAR"]], "eraytma()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.ERAYTMA"]], "fibonaccima()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.FibonacciMA"]], "gmma()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.GMMA"]], "macdboll()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.MACDBOLL"]], "pp_camarilla()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.PP_Camarilla"]], "pp_demarks()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.PP_DeMarks"]], "pp_fibonacci()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.PP_Fibonacci"]], "pp_standard()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.PP_Standard"]], "pp_woodie()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.PP_Woodie"]], "tradma()\uff08\u5728 indicator_arsenals \u6a21\u5757\u4e2d\uff09": [[19, "indicator_arsenals.TRADMA"]], "indicator_arsenals": [[19, "module-indicator_arsenals"]], "bardatalist\uff08object \u4e2d\u7684\u7c7b\uff09": [[22, "object.BarDataList"]], "marketdatalist\uff08object \u4e2d\u7684\u7c7b\uff09": [[22, "object.MarketDataList"]], "object": [[22, "module-object"]], "get()\uff08\u5728 param \u6a21\u5757\u4e2d\uff09": [[23, "param.get"]], "get_group_names()\uff08\u5728 param \u6a21\u5757\u4e2d\uff09": [[23, "param.get_group_names"]], "get_matrix_np()\uff08\u5728 param \u6a21\u5757\u4e2d\uff09": [[23, "param.get_matrix_np"]], "get_matrix_pb()\uff08\u5728 param \u6a21\u5757\u4e2d\uff09": [[23, "param.get_matrix_pb"]], "param": [[23, "module-param"]], "error()\uff08\u5728 qlog \u6a21\u5757\u4e2d\uff09": [[25, "qlog.error"]], "error_f()\uff08\u5728 qlog \u6a21\u5757\u4e2d\uff09": [[25, "qlog.error_f"]], "info()\uff08\u5728 qlog \u6a21\u5757\u4e2d\uff09": [[25, "qlog.info"]], "info_f()\uff08\u5728 qlog \u6a21\u5757\u4e2d\uff09": [[25, "qlog.info_f"]], "qlog": [[25, "module-qlog"]], "quick_start": [[26, "module-quick_start"]], "run_daily()\uff08\u5728 scheduler \u6a21\u5757\u4e2d\uff09": [[27, "scheduler.run_daily"]], "run_second()\uff08\u5728 scheduler \u6a21\u5757\u4e2d\uff09": [[27, "scheduler.run_second"]], "scheduler": [[27, "module-scheduler"]], "cci()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.CCI"]], "kelchan()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.KELCHAN"]], "ma()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.MA"]], "macd()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.MACD"]], "mfi()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.MFI"]], "mom()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.MOM"]], "obv()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.OBV"]], "rsi()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.RSI"]], "stoch()\uff08\u5728 talib_erayt \u6a21\u5757\u4e2d\uff09": [[28, "talib_erayt.STOCH"]], "talib_erayt": [[28, "module-talib_erayt"]], "check_spread()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.check_spread"]], "close_all()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.close_all"]], "get_count_orders()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.get_count_orders"]], "init_count_orders()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.init_count_orders"]], "open_buy()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.open_buy"]], "open_sell()\uff08\u5728 trade_arsenals \u6a21\u5757\u4e2d\uff09": [[29, "trade_arsenals.open_sell"]], "trade_arsenals": [[29, "module-trade_arsenals"]], "equantapi.xrisk": [[31, "module-equantapi.xrisk"]], "risk_param_init()\uff08\u5728 equantapi.xrisk \u6a21\u5757\u4e2d\uff09": [[31, "equantapi.xrisk.risk_param_init"]], "than_max_loss()\uff08\u5728 equantapi.xrisk \u6a21\u5757\u4e2d\uff09": [[31, "equantapi.xrisk.than_max_loss"]], "than_today_max_loss()\uff08\u5728 equantapi.xrisk \u6a21\u5757\u4e2d\uff09": [[31, "equantapi.xrisk.than_today_max_loss"]], "than_today_max_orders()\uff08\u5728 equantapi.xrisk \u6a21\u5757\u4e2d\uff09": [[31, "equantapi.xrisk.than_today_max_orders"]], "than_today_max_undo_orders()\uff08\u5728 equantapi.xrisk \u6a21\u5757\u4e2d\uff09": [[31, "equantapi.xrisk.than_today_max_undo_orders"]]}})