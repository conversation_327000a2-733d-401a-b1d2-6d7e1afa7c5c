<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>持仓API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="做市API" href="maker.html" />
    <link rel="prev" title="行情API" href="md.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html">固收API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3"><a class="reference internal" href="md.html">行情API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">持仓API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_bond_dimension_loss_profit"><code class="docutils literal notranslate"><span class="pre">get_bond_dimension_loss_profit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_bond_loss_profit"><code class="docutils literal notranslate"><span class="pre">get_bond_loss_profit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_dv01"><code class="docutils literal notranslate"><span class="pre">get_dv01()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_indicators"><code class="docutils literal notranslate"><span class="pre">get_indicators()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_ord_position"><code class="docutils literal notranslate"><span class="pre">get_ord_position()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_position"><code class="docutils literal notranslate"><span class="pre">get_position()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_position_onroad"><code class="docutils literal notranslate"><span class="pre">get_position_onroad()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.get_xswap_loss_profit"><code class="docutils literal notranslate"><span class="pre">get_xswap_loss_profit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#pos.query_coupon_pool_indicators"><code class="docutils literal notranslate"><span class="pre">query_coupon_pool_indicators()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
<li class="toctree-l3"><a class="reference internal" href="signal.html">信号API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../fx/index.html">外汇API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">固收API</a></li>
      <li class="breadcrumb-item active">持仓API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/bond/pos.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.pos">
<span id="api"></span><h1>持仓API<a class="headerlink" href="#module-.pos" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="pos.get_bond_dimension_loss_profit">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_bond_dimension_loss_profit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dimension</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">liquidation</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dealType</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bonds_classification</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_bond_dimension_loss_profit" title="Link to this definition"></a></dt>
<dd><p>现券损益查询</p>
<dl>
<dt>参数:</dt><dd><p>dimension(int): 查询维度 0-债券维度 1-策略维度 2-债券分类</p>
<p>liquidation(int): 清算速度维度 0:T+0 1:T+1 默认 -1:全部,只有查询维度为债券维度时候需要输入该数值,默认为-1</p>
<p>dealType(str): 债券名称, 当查询维度为1(策略)时,不需要输入</p>
<p>bonds_classification(int): 债券分类, 1-国债 2-国开债 3-进出口行债 4-农发行债</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>quantity(float):敞口</p>
<p>cost_price(float):成本价</p>
<p>trade_interest(float):交易利息</p>
<p>pay_date_income(float):付息日利息收入</p>
<p>accrued_interest(float):应计利息</p>
<p>capital_gains(float):资本利得</p>
<p>loan_fee(float):债券借贷费</p>
<p>float_pl(float):浮动损益</p>
<p>profit(float): 总损益</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_bond_dimension_loss_profit</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">dealType</span><span class="o">=</span><span class="s1">&#39;160017&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_bond_loss_profit">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_bond_loss_profit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dimension</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_bond_loss_profit" title="Link to this definition"></a></dt>
<dd><p>现券损益查询</p>
<dl>
<dt>参数:</dt><dd><p>dimension(int): 查询维度 1-合约 2-策略</p>
<p>symbol(str): 合约代码, 当查询维度为2(策略)时,不需要输入</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>unRealizedPL(float): 未实现损益</p>
<p>realizedPL(int): 已实现损益</p>
<p>profit(float): 总损益</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_bond_loss_profit</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">symbol</span><span class="o">=</span><span class="s1">&#39;160017_T+1&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_dv01">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_dv01</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dimension</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_dv01" title="Link to this definition"></a></dt>
<dd><p>dv01查询(基准指标)利率互换</p>
<dl>
<dt>参数:</dt><dd><p>dimension(int): 合约类型 0-基准指标 1-策略</p>
<p>symbol(str): 基准指标</p>
</dd>
<dt>返回:</dt><dd><p>利率互换:</p>
<dl>
<dt>合约类型传入0:</dt><dd><p>基准指标传入None时:</p>
<blockquote>
<div><p>dict:</p>
<blockquote>
<div><p>{</p>
<p>‘unit_dv01’:</p>
<blockquote>
<div><p>{symbol:{key – 期限点(str) : value – dv01(float)},</p>
<p>symbol:{key – 期限点(str) : value – dv01(float)}},</p>
</div></blockquote>
<p>‘tactics_sublevel_dv01_and_all_dv01’:</p>
<blockquote>
<div><blockquote>
<div><p>{key – 期限点(str) : value – dv01(float)}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
</div></blockquote>
</div></blockquote>
<p>基准指标传入值时:</p>
<blockquote>
<div><p>dict:</p>
<blockquote>
<div><p>{</p>
<p>‘unit_dv01’:</p>
<blockquote>
<div><p>{{key – 期限点(str) : value – dv01(float)},</p>
</div></blockquote>
<p>‘<a href="#id1"><span class="problematic" id="id2">index_</span></a>’+symbol+’_sublevel_dv01_and_all_dv01’:</p>
<blockquote>
<div><p>{key – 期限点(str) : value – dv01(float)}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
</div></blockquote>
</dd>
<dt>合约类型传入1:</dt><dd><p>基准指标传入None时:</p>
<blockquote>
<div><p>dict:</p>
<blockquote>
<div><p>{</p>
<p>‘tactics_sublevel_dv01_and_all_dv01’:</p>
<blockquote>
<div><p>{key – 期限点(str) : value – dv01(float)}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
</div></blockquote>
<p>基准指标传入值时:</p>
<blockquote>
<div><p>dict:</p>
<blockquote>
<div><p>{</p>
<p>‘<a href="#id3"><span class="problematic" id="id4">index_</span></a>’+symbol+’_sublevel_dv01_and_all_dv01’:</p>
<blockquote>
<div><p>{key – 期限点(str) : value – dv01(float)}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
</div></blockquote>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_dv01</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">symbol</span><span class="o">=</span><span class="s1">&#39;FR007&#39;</span><span class="p">)</span>
<span class="go">    dict:</span>
<span class="go">        {</span>
<span class="go">            &#39;unit_dv01&#39;: {</span>
<span class="go">                &#39;1W&#39;: 0.000128515849878681,</span>
<span class="go">                &#39;1M&#39;: 0.000511175662487819,</span>
<span class="go">                &#39;3M&#39;: 0.001504189594754379,</span>
<span class="go">                &#39;6M&#39;: 0.003150001287815192,</span>
<span class="go">                &#39;9M&#39;: 0.004549469498652091,</span>
<span class="go">                &#39;1Y&#39;: 0.006196887571089486,</span>
<span class="go">                &#39;2Y&#39;: 0.011579257279061596,</span>
<span class="go">                &#39;3Y&#39;: 0.017673877414448803,</span>
<span class="go">                &#39;4Y&#39;: 0.02495314119810738,</span>
<span class="go">                &#39;5Y&#39;: 0.030532689279085506</span>
<span class="go">            },</span>
<span class="go">            &#39;index_&#39;+symbol+&#39;_sublevel_dv01_and_all_dv01&#39;: {</span>
<span class="go">                &#39;1W&#39;: 0.0,</span>
<span class="go">                &#39;1M&#39;: 0.0,</span>
<span class="go">                &#39;3M&#39;: 0.0,</span>
<span class="go">                &#39;6M&#39;: 0.0,</span>
<span class="go">                &#39;9M&#39;: 0.0,</span>
<span class="go">                &#39;1Y&#39;: 0.0,</span>
<span class="go">                &#39;2Y&#39;: 0.0,</span>
<span class="go">                &#39;3Y&#39;: 0.0,</span>
<span class="go">                &#39;4Y&#39;: 0.0,</span>
<span class="go">                &#39;5Y&#39;: 0.0,</span>
<span class="go">                &#39;ALL&#39;: -944.4172199859313</span>
<span class="go">            }</span>
<span class="go">        }</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_indicators">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_indicators</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbols</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_indicators" title="Link to this definition"></a></dt>
<dd><p>金融指标查询</p>
<dl>
<dt>可选参数：</dt><dd><p>symbols(list or str):合约编码</p>
</dd>
<dt>返回：</dt><dd><p>returns:dict</p>
<p>单合约:</p>
<blockquote>
<div><p>固息债|贴现|利随本清:</p>
<blockquote>
<div><p>duration(float): 久期</p>
<p>mod_duration(float): 修正久期</p>
<p>convexity(float): 凸性</p>
<p>dv01(float): dv01(未产生敞口时返回单位值)</p>
</div></blockquote>
<p>浮息债:</p>
<blockquote>
<div><p>spread_duration(float): 利差久期</p>
<p>spread_convexity(float): 利差凸性</p>
<p>ir_duration(float): 利率久期</p>
<p>ir_convexity(float): 利率凸性</p>
<p>dv01(float): dv01(未产生敞口时返回单位值)</p>
</div></blockquote>
</div></blockquote>
<p>投组</p>
<blockquote>
<div><p>投组维度如果未输入合约查询策略维度的指标，当未产生敞口时，返回空。
投组维度如果输入合约，查询指定合约维度，当未产生敞口时，返回单位指标</p>
<blockquote>
<div><p>duration(float): 久期(浮息债为利差久期)</p>
<p>convexity(float): 凸性(浮息债为利差凸性)</p>
<p>dv01(float): dv01</p>
</div></blockquote>
</div></blockquote>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_indicators</span><span class="p">(</span><span class="s1">&#39;160016_T+1&#39;</span><span class="p">)</span>
<span class="go">    {&quot;duration&quot;: 1.355, &quot;mod_duration&quot;: 1.6933, &quot;convexity&quot;: 2.355, &quot;dv01&quot;: 1.35443}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_ord_position">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_ord_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">order_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#pos.get_ord_position" title="Link to this definition"></a></dt>
<dd><p>根据合约或者开仓订单ID获取逐笔持仓信息,如果不传获取全部未平仓持仓</p>
<dl>
<dt>可选参数:</dt><dd><p>order_id(string):开仓订单Id (default: {None})</p>
<p>symbol(string):合约代码 (default: {None})</p>
</dd>
<dt>返回:</dt><dd><p>list&lt;dict&gt;: Position信息</p>
<p>Returns:list</p>
<p>[{</p>
<blockquote>
<div><p>id’(int): 唯一编号</p>
<p>symbol(string): 合约代码</p>
<p>frozenQuantity(float): 冻结量</p>
<p>quantity(float): 总持仓量</p>
<p>quantityTd(float): 今日持仓量</p>
<p>posSide(int): 头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向](default: {0})</p>
<p>profit(float): 损益</p>
<p>value(float): 估值</p>
<p>costPrice(float): 敞口价格</p>
<p>unRealizedPL(float): 未交割浮动损益</p>
<p>realizedPL(int): 已交割损益</p>
<p>washAmount(float): 持仓成本</p>
<p>time(long): 头寸时间</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_ord_position</span><span class="p">(</span><span class="mi">216868121676222464</span><span class="p">)</span>
<span class="go">    [{&#39;id&#39;: 216868121676222464, &#39;symbol&#39;: &#39;EURUSDSP&#39;, &#39;frozenQuantity&#39;: 0, &#39;quantity&#39;: 0, &#39;quantityTd&#39;: 0, &#39;posSide&#39;: 0,</span>
<span class="go">    &#39;profit&#39;: -2145.0000000000637, &#39;value&#39;: 0.0, &#39;costPrice&#39;: 1.16064, &#39;unRealizedPL&#39;: 0.0,</span>
<span class="go">    &#39;realizedPL&#39;: -2145.0000000000637, &#39;washAmount&#39;: 0.0, &#39;time&#39;: 1530524400101}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_position">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_position</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">dict</span></span></span><a class="headerlink" href="#pos.get_position" title="Link to this definition"></a></dt>
<dd><p>根据合约获取持仓信息</p>
<dl>
<dt>必选参数:</dt><dd><p>symbol(string):合约代码</p>
</dd>
<dt>可选参数:</dt><dd><p>pos_side(int):头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向](default: {0})</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>symbol(string): 合约代码</p>
<p>frozenQuantity(float): 冻结量</p>
<p>quantity(float): 总持仓量</p>
<p>posSide(int): 头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向](default: {0})</p>
<p>profit(float): 损益</p>
<p>value(float): 估值</p>
<p>costPrice(float): 敞口价格</p>
<p>unRealizedPL(float): 未交割浮动损益</p>
<p>realizedPL(float): 已交割损益</p>
<p>washAmount(float): 持仓成本</p>
<p>time(long): 头寸时间</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_position</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">    {&#39;symbol&#39;: &#39;EURUSDSP&#39;, &#39;frozenQuantity&#39;: 0, &#39;quantity&#39;: 0, &#39;posSide&#39;: 0,</span>
<span class="go">    &#39;profit&#39;: -2145.0000000000637, &#39;value&#39;: 0.0, &#39;costPrice&#39;: 1.16064, &#39;unRealizedPL&#39;: 0.0,</span>
<span class="go">    &#39;realizedPL&#39;: -2145.0000000000637, &#39;washAmount&#39;: 0.0, &#39;time&#39;: 1530524400101}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_position_onroad">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_position_onroad</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">dict</span></span></span><a class="headerlink" href="#pos.get_position_onroad" title="Link to this definition"></a></dt>
<dd><p>根据合约获取持仓信息和在途单量</p>
<dl>
<dt>必选参数:</dt><dd><p>symbol(string):合约代码</p>
</dd>
<dt>可选参数:</dt><dd><p>effect(int):–&gt;EffectEnum[0-中性,1-开仓,2-平仓](default: {0})</p>
<p>pos_side(int):头寸方向–&gt;PosSideEnum[0-中性,1-多方向,2-空方向](default: {0})</p>
</dd>
<dt>返回:</dt><dd><p>dict: 各方向在途单数量及总持仓</p>
<p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>onroad_b(str): 买方向汇总</p>
<p>onroad_s(float): 卖方向汇总</p>
<p>quantity(float): 总持仓</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">get_position_onroad</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">    {&quot;onroad_b&quot;,60.0,&quot;onroad_s&quot;,50.0,&quot;quantity&quot;:1000}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.get_xswap_loss_profit">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">get_xswap_loss_profit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dimension</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.get_xswap_loss_profit" title="Link to this definition"></a></dt>
<dd><p>利率互换损益查询</p>
<dl>
<dt>参数:</dt><dd><p>dimension(int): 查询维度 1-利率指标 2-策略</p>
<p>symbol(str): 利率指标代码(如 FR007,Shibor3M), 当查询维度为2(策略)时,不需要输入</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>unRealizedPL(float): 未实现损益</p>
<p>realizedPL(float): 已实现损益</p>
<p>profit(float): 总损益</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">value</span> <span class="o">=</span> <span class="n">pos</span><span class="o">.</span><span class="n">get_xswap_loss_profit</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">symbol</span><span class="o">=</span><span class="s1">&#39;FR007&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pos.query_coupon_pool_indicators">
<span class="sig-prename descclassname"><span class="pre">固收.pos.</span></span><span class="sig-name descname"><span class="pre">query_coupon_pool_indicators</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coupon_pool_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_termtomaturity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_termtomaturity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pos.query_coupon_pool_indicators" title="Link to this definition"></a></dt>
<dd><p>券池金融指标查询
参数:</p>
<blockquote>
<div><p>coupon_pool_code(str):券池编码</p>
<p>source(str):渠道</p>
<p>start_termtomaturity(int)   待偿期起始,非必输</p>
<p>end_termtomaturity(int)     待偿期结束,非必输</p>
</div></blockquote>
<dl>
<dt>返回：</dt><dd><p>returns:dict</p>
<p>{   bond_code(str): {</p>
<blockquote>
<div><p>duration(float): 久期</p>
<p>mod_duration(float): 修正久期</p>
<p>convexity(float): 凸性</p>
<p>dv01_unit(float): 单位dv01</p>
<p>}</p>
</div></blockquote>
<p>}</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pos</span><span class="o">.</span><span class="n">query_coupon_pool_indicators</span><span class="p">(</span><span class="s1">&#39;国债&#39;</span><span class="p">,</span> <span class="s1">&#39;X-BOND_HO&#39;</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="mi">3</span><span class="p">)</span>
<span class="go">    {&#39;160017&#39;: {&quot;duration&quot;: 1.355, &quot;mod_duration&quot;: 1.6933, &quot;convexity&quot;: 2.355, &quot;dv01_unit&quot;: 1.35443}}</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="md.html" class="btn btn-neutral float-left" title="行情API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="maker.html" class="btn btn-neutral float-right" title="做市API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>