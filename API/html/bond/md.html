<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>行情API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="持仓API" href="pos.html" />
    <link rel="prev" title="订单API" href="deal.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html">固收API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">行情API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#md.cal_bond_yield_curve_indicator"><code class="docutils literal notranslate"><span class="pre">cal_bond_yield_curve_indicator()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_active_bond"><code class="docutils literal notranslate"><span class="pre">get_active_bond()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_bond_mutual_calculation"><code class="docutils literal notranslate"><span class="pre">get_bond_mutual_calculation()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_bond_residual_curve"><code class="docutils literal notranslate"><span class="pre">get_bond_residual_curve()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_bond_yield_curve"><code class="docutils literal notranslate"><span class="pre">get_bond_yield_curve()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_bond_yield_curve_slope"><code class="docutils literal notranslate"><span class="pre">get_bond_yield_curve_slope()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_cn_bond_evaluation"><code class="docutils literal notranslate"><span class="pre">get_cn_bond_evaluation()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_depth_info_quoteid"><code class="docutils literal notranslate"><span class="pre">get_depth_info_quoteid()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_irs_df"><code class="docutils literal notranslate"><span class="pre">get_irs_df()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_irs_fixing_curve"><code class="docutils literal notranslate"><span class="pre">get_irs_fixing_curve()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_price"><code class="docutils literal notranslate"><span class="pre">get_price()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.get_xswap_curve"><code class="docutils literal notranslate"><span class="pre">get_xswap_curve()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.inactiveBondPricing"><code class="docutils literal notranslate"><span class="pre">inactiveBondPricing()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.put_maker_symbol"><code class="docutils literal notranslate"><span class="pre">put_maker_symbol()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_bar_spreads"><code class="docutils literal notranslate"><span class="pre">query_bar_spreads()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_bars"><code class="docutils literal notranslate"><span class="pre">query_bars()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_bars_pro"><code class="docutils literal notranslate"><span class="pre">query_bars_pro()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_coupons_pool_bars"><code class="docutils literal notranslate"><span class="pre">query_coupons_pool_bars()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#md.query_spreads"><code class="docutils literal notranslate"><span class="pre">query_spreads()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="pos.html">持仓API</a></li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
<li class="toctree-l3"><a class="reference internal" href="signal.html">信号API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../fx/index.html">外汇API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">固收API</a></li>
      <li class="breadcrumb-item active">行情API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/bond/md.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.md">
<span id="api"></span><h1>行情API<a class="headerlink" href="#module-.md" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="md.cal_bond_yield_curve_indicator">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">cal_bond_yield_curve_indicator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_date</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_tenor</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.cal_bond_yield_curve_indicator" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>关期限点指定时间段的曲线查询(仅支持回测)</p>
</div></blockquote>
<dl>
<dt>参数:</dt><dd><p>code(str):合约</p>
<p>start_date(int):起始日期 日期格式YYYYMMDD</p>
<p>end_date(int):结束日期 日期格式YYYYMMDD 默认是当前时间</p>
<p>key_tenor(list):关键期限点 默认查询全部 期限枚举[‘1D’, ‘1W’, ‘3M’, ‘6M’, ‘9M’, ‘1Y’, ‘2Y’, ‘3Y’, ‘4Y’, ‘5Y’]</p>
<p>data_type(int):数据返回类型 0 表示pandas结构;2 表示dict结构(default: dict{2})</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<blockquote>
<div><p>{</p>
<blockquote>
<div><p>“date”: {</p>
<blockquote>
<div><p>tenor : rate</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<dl class="simple">
<dt>或者: dataFrame</dt><dd><p>1D        1W</p>
</dd>
</dl>
<p>20240527  2.299928  2.299547</p>
<p>20240604  2.299928  2.299547</p>
<p>20240605  2.299928  2.299547</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">rate</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">cal_bond_yield_curve_indicator</span><span class="p">(</span><span class="s1">&#39;FR007&#39;</span><span class="p">,</span> <span class="mi">20220608</span><span class="p">,</span> <span class="mi">20220701</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;1D&#39;</span><span class="p">,</span><span class="s1">&#39;1W&#39;</span><span class="p">])</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_active_bond">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_active_bond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">channel_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bond_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_date</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_date</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_active_bond" title="Link to this definition"></a></dt>
<dd><p>活跃券/新老券 切券信息查询</p>
<dl>
<dt>参数:</dt><dd><p>channel_code(str):渠道编码</p>
<p>bond_code(str):虚拟合约编码</p>
<p>start_date(int):开始时间</p>
<p>end_date(int):结束时间</p>
<p>data_type int    数据返回类型 0 表示pandas结构 2 表示dict结构(default: {1})</p>
</dd>
<dt>返回:</dt><dd><p>活跃券切券信息</p>
<p>Returns:list</p>
<dl>
<dt>[{</dt><dd><p>virtualCode(str): 虚拟合约编码</p>
<p>contractCode(str): 合约编码</p>
<p>date(int): 日期</p>
<p>channel(str): 渠道</p>
<p>type(str): 产品类型</p>
<p>time(int): 切券日期</p>
</dd>
</dl>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">get_active_bond</span><span class="p">(</span><span class="s1">&#39;X-BOND_HO&#39;</span><span class="p">,</span> <span class="s1">&#39;100001#1M&#39;</span><span class="p">,</span> <span class="mi">20221001</span><span class="p">,</span> <span class="mi">20231001</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_bond_mutual_calculation">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_bond_mutual_calculation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">netPrice</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ytm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_bond_mutual_calculation" title="Link to this definition"></a></dt>
<dd><p>互算接口: 输入净价或者到期收益率计算返回净价、全价、到期收益率</p>
<dl>
<dt>参数:</dt><dd><p>symbol(str):债券编码 必输</p>
<p>netprice(float):净价 非必输,与到期收益率二选一输入即可</p>
<p>ytm(float):到期收益率 非必输,与净价二选一输入即可</p>
</dd>
<dt>返回:</dt><dd><p>netprice(float):净价</p>
<p>fullprice(float):全价</p>
<p>ytm(float):到期收益率</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">discount_factor</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_bond_mutual_calculation</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">netPrice</span><span class="p">,</span> <span class="n">ytm</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_bond_residual_curve">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_bond_residual_curve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">curveType</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'1N'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">num</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_bond_residual_curve" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><blockquote>
<div><p>获取债券的残差曲线</p>
</div></blockquote>
<p>参数:</p>
<blockquote>
<div><p>symbol(str): 债券编码</p>
<p>curveType(int): 曲线类型</p>
</div></blockquote>
</div></blockquote>
<dl>
<dt>0: 全部 1:’GB’ 2: ‘GB_HW’ 3: ‘GB_NS’</dt><dd><blockquote>
<div><p>num(int): 数量, 默认可不填</p>
<p>frequency(str): 残差曲线的周期 频率–&gt;[1N,5N,15N,30N,1H,1D,1W,1M]</p>
<p>data_type(int):数据返回类型 0 表示pandas结构; 1 表示numpy结构; 2 表示dict结构(default: dict{2})</p>
</div></blockquote>
<p>其他参数:</p>
<blockquote>
<div><p>start_datatime str bar的开始时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
<p>end_datatime str bar的结束时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
</div></blockquote>
<p>返回:</p>
<blockquote>
<div><p>Returns:dict结构{</p>
<blockquote>
<div><p>curve(np.arr):{</p>
<blockquote>
<div><p>symbol(np.str):合约代码</p>
<p>time(np.int64):时间</p>
<p>price_residual(np.float64):全价残差</p>
<p>yield_residual(np.float64):到期收益率残差</p>
</div></blockquote>
<p>}</p>
<p>price_residual_avg(float64):全价残差均值</p>
<p>price_residual_std(float64):全价残差标准差</p>
<p>price_residual_var(float64):全价残差方差</p>
<p>yield_residual_avg(float64):到期收益率残差均值</p>
<p>yield_residual_std(float64):到期收益率残差标准差</p>
<p>yield_residual_var(float64):到期收益率残差方差</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">slope</span><span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_bond_residual_curve</span><span class="p">(</span><span class="s2">&quot;160017_T+1&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">frequency</span><span class="o">=</span><span class="s1">&#39;1N&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_bond_yield_curve">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_bond_yield_curve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">curve_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bond_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_tenor</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_bond_yield_curve" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>债券收益率曲线查询</p>
<dl>
<dt>参数:</dt><dd><p>curve_type(int):曲线类型 1-自定义曲线 2-中债曲线</p>
<p>bond_type(str):债券类型 CDB-政策性金融债(国开行) GB-国债 EIBC-政策性金融债(进出口行) ADBC-政策性金融债(农发行)</p>
<blockquote>
<div><p>注意: 在选择自定义曲线的时候 有两种算法构建, 为Hermite算法,Hull-White 算法, 如果选择前者, 债券类型不加_HW, 例如 GZ;如果选择后者算法,需要在债券类型后加_HW, 例如GZ_HW</p>
</div></blockquote>
</dd>
</dl>
</div></blockquote>
<dl>
<dt></dt><dd><blockquote>
<div><p>query_type(int):查询类型 0-全部曲线 1-即期 2-远期 3-到期</p>
<p>key_tenor(float list):关键期限点</p>
<p>data_type(int):数据返回类型 0 表示pandas结构; 1 表示numpy结构; 2 表示dict结构(default: dict{2})</p>
</div></blockquote>
<p>其他参数:</p>
<blockquote>
<div><p>start_datatime str bar的开始时间,默认为昨天的当前时间 支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
<p>end_datatime str bar的结束时间,默认为昨天的当前时间 支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
</div></blockquote>
<dl>
<dt>返回:</dt><dd><p>Returns:list</p>
<blockquote>
<div><p>[</p>
<blockquote>
<div><p>{“data”:</p>
<blockquote>
<div><p>{</p>
<p>spot(dict):即期曲线</p>
<blockquote>
<div><p>key:关键期限点</p>
<p>value:关键期限点对应的即期收益率</p>
</div></blockquote>
<p>fwd(dict):远期曲线</p>
<blockquote>
<div><p>key:关键期限点</p>
<p>value:关键期限点对应的远期收益率</p>
</div></blockquote>
<p>maturity(dict):到期曲线</p>
<blockquote>
<div><p>key:关键期限点</p>
<p>value:关键期限点对应的到期收益率</p>
</div></blockquote>
<p>},</p>
</div></blockquote>
<p>“time”:时间戳</p>
<p>}</p>
</div></blockquote>
<p>]</p>
</div></blockquote>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">curve</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_bond_yield_curve</span><span class="p">(</span><span class="n">curve_type</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bond_type</span><span class="o">=</span><span class="s1">&#39;GK&#39;</span><span class="p">,</span> <span class="n">query_type</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">key_tenor</span><span class="o">=</span><span class="mf">0.09</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_bond_yield_curve_slope">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_bond_yield_curve_slope</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">curve_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bond_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_tenor_a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key_tenor_b</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_bond_yield_curve_slope" title="Link to this definition"></a></dt>
<dd><p>计算曲线斜率</p>
<dl>
<dt>参数:</dt><dd><p>curve_type(int):曲线类型 1-自定义曲线 2-中债曲线</p>
<p>bond_type(str):债券类型 CDB-政策性金融债(国开行) GB-国债 EIBC-政策性金融债(进出口行) ADBC-政策性金融债(农发行)</p>
<blockquote>
<div><p>注意: 在选择自定义曲线的时候 有两种算法构建, 为Hermite算法,Hull-White 算法, 如果选择前者, 债券类型不加_HW, 例如 GZ;如果选择后者算法,需要在债券类型后加_HW, 例如GZ_HW</p>
</div></blockquote>
<p>query_type(int):查询类型 0-全部曲线 1-即期 2-远期 3-到期</p>
<p>key_tenor_a(float):期限点a</p>
<p>key_tenor_b(float):期限点b</p>
</dd>
<dt>返回:</dt><dd><p>float 类型的斜率</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">slope</span><span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_bond_yield_curve_slope</span><span class="p">(</span><span class="n">curve_type</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bond_type</span><span class="o">=</span><span class="s1">&#39;GK&#39;</span><span class="p">,</span> <span class="n">query_type</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">key_tenor_a</span><span class="o">=</span><span class="mf">0.09</span><span class="p">,</span> <span class="n">key_tenor_b</span><span class="o">=</span><span class="mf">0.09</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_cn_bond_evaluation">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_cn_bond_evaluation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_cn_bond_evaluation" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>中债估值数据查询 上一日估值数据,无数据返回None</p>
</div></blockquote>
<p>参数:</p>
<blockquote>
<div><p>symbol(str): 合约唯一代码 必输</p>
</div></blockquote>
<p>返回:</p>
<blockquote>
<div><p>Returns:dict</p>
<p>{</p>
<blockquote>
<div><p>bond_code(str): 债券编码</p>
<p>bond_name(str): 债券简称</p>
<p>bond_type_name(str): 债券类型</p>
<p>valuation_date(int): 估值日期</p>
<p>termtomaturity(str): 待偿期</p>
<p>price(float): 估值净价</p>
<p>ytm(float): 估值收益率</p>
<p>accrued_interest(float): 应计利息</p>
<p>duration(float): 久期</p>
<p>convex(float): 凸性</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">get_cn_bond_evaluation</span><span class="p">(</span><span class="s1">&#39;160017_T+1&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_depth_info_quoteid">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_depth_info_quoteid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">side</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_depth_info_quoteid" title="Link to this definition"></a></dt>
<dd><p>做市点击单订单报价id查询 无数据返回None</p>
<dl>
<dt>参数:</dt><dd><p>symbol(str):合约唯一代码</p>
<p>type(str): 产品小类</p>
<p>source(str):渠道</p>
<p>side(str):买卖方向  买方向B的单子 需要输入的price为asks  卖方向S的单子 需要输入的price为bids   买卖方向和后续to_order保持一致</p>
<p>price(float):  每个方向的 行情有1-5档</p>
<p>data(list): 行情数据</p>
</dd>
<dt>返回:</dt><dd><p>Returns:quoteid (str)</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">quoteid</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_depth_info_quoteid</span><span class="p">(</span><span class="s2">&quot;160017_T+1&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;BONDSDEAL&#39;</span><span class="p">,</span> <span class="n">source</span><span class="o">=</span><span class="s2">&quot;CFETS-QDM_HO&quot;</span><span class="p">,</span> <span class="n">side</span><span class="o">=</span><span class="s1">&#39;B&#39;</span><span class="p">,</span> <span class="n">price</span><span class="o">=</span><span class="n">data</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="s1">&#39;asks&#39;</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">data</span><span class="o">=</span><span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_irs_df">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_irs_df</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">date_list</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">list</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_irs_df" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>贴现因子查询</p>
</div></blockquote>
<dl>
<dt>参数:</dt><dd><p>code(str):合约</p>
<p>date_list(list):日期列表 日期格式YYYYMMDD 日期在五年以内</p>
<p>data_type(int):数据返回类型 0 表示pandas结构; 1 表示numpy结构; 2 表示dict结构(default: dict{2})</p>
</dd>
</dl>
<p>其他参数:</p>
<blockquote>
<div><p>start_datatime str bar的开始时间,默认为昨天的当前时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
<p>end_datatime str bar的结束时间,默认为今天的当前时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
</div></blockquote>
<dl>
<dt>返回:</dt><dd><p>Returns:list</p>
<p>[</p>
<blockquote>
<div><p>{‘data’: {</p>
<blockquote>
<div><p>20240608:</p>
<blockquote>
<div><p>0.9696126290413021,</p>
</div></blockquote>
<p>20240701:</p>
<blockquote>
<div><p>0.9682810793669262},</p>
</div></blockquote>
</div></blockquote>
<p>‘time’: 1672988400000}, 当前时间戳</p>
<p>{‘data’: {</p>
<blockquote>
<div><p>20240608:</p>
<blockquote>
<div><p>0.9796126290413021,</p>
</div></blockquote>
<p>20240701:</p>
<blockquote>
<div><p>0.9782810793669262},</p>
</div></blockquote>
</div></blockquote>
<p>‘time’: 1672988900000} 当前时间戳</p>
</div></blockquote>
<p>]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">discount_factor</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_irs_df</span><span class="p">(</span><span class="s1">&#39;FR007&#39;</span><span class="p">,</span> <span class="p">[</span><span class="mi">20220608</span><span class="p">,</span> <span class="mi">20220701</span><span class="p">])</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_irs_fixing_curve">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_irs_fixing_curve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_date</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_irs_fixing_curve" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>定盘利率查询,无数据返回None</p>
</div></blockquote>
<dl>
<dt>参数:</dt><dd><p>symbol(str):合约</p>
<p>start_date(list):起始日期  日期格式YYYYMMDD</p>
<p>end_date(list):结束日期  日期格式YYYYMMDD 默认是当前时间</p>
<p>data_type(int):数据返回类型 0 表示pandas结构;2 表示dict结构(default: dict{2})</p>
</dd>
<dt>返回:</dt><dd><p>Returns:dict</p>
<blockquote>
<div><dl>
<dt>{</dt><dd><p>“date”: {</p>
<blockquote>
<div><p>mdType(str): 债券类型 0-Shibor，K-回购定盘,</p>
<p>securityType(str): 债券品种 Shibor：ShiborCn 回购定盘：FR001、FR007、FR014,</p>
<p>tenor(str): 债券期限 Shibor：O/N、1W、2W、1M、3M、6M、9M、1Y，回购定盘：上下限（例：1-5）,</p>
<p>price(float): 价格,</p>
<p>shiborBp(str): 涨跌幅 Shibor专有，单位BP ,</p>
<p>benchmarkEffectiveDate(str): 生成日期 格式yyyyMMdd</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">rate</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_irs_fixing_curve</span><span class="p">(</span><span class="s1">&#39;FR007&#39;</span><span class="p">,</span> <span class="mi">20220608</span><span class="p">,</span> <span class="mi">20220701</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_price">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_price</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#md.get_price" title="Link to this definition"></a></dt>
<dd><p>获取最新行情</p>
<dl>
<dt>必填参数:</dt><dd><p>symbol(str):合约唯一代码 注意:当调用活跃券合约代码时,其返回对应真实券行情数据</p>
</dd>
<dt>其他参数:</dt><dd><p>type(str):数据类型(default: {None})</p>
<p>source(str):行情来源(default: {None})</p>
<p>fields(list):指定返回对象字段(default: {None})</p>
</dd>
<dt>返回:</dt><dd><p>行情对象 list&lt;dict&gt;</p>
<p>Returns:list</p>
<p>[{</p>
<blockquote>
<div><p>status(str): 价格状态 –&gt;QuoteStatusEnum[1 - 正常, 2 - 异常]</p>
<p>source(str): 数据渠道</p>
<p>type(str): 数据类型</p>
<p>symbol(str): 合约代码</p>
<p>time(int): 时间戳</p>
<p>best_bid(float): 最优买价</p>
<p>best_bid_amt(float): 最优买价数量</p>
<p>best_ask(float): 最优卖价</p>
<p>best_ask_amt(float): 最优卖价数量</p>
<p>asks(list): 卖出报盘价格，asks[0] 代表盘口卖一档报盘价</p>
<p>ask_vols(list): 卖出报盘数量，ask_vols[0]代表盘口卖一档报盘数量</p>
<p>bids(list): 买入报盘价格，bids[0]代表盘口买一档报盘价</p>
<p>bid_vols(list): 买入报盘数量，bid_vols[0]代表盘口买一档报盘数量</p>
<p>limit_up(float): 涨停价</p>
<p>limit_down(float): 跌停价</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">get_price</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">)</span>
<span class="go">    [{&#39;status&#39;: &#39;1&#39;, &#39;source&#39;: &#39;CFETS_LC&#39;, &#39;type&#39;: &#39;ODM_DEPTH&#39;, &#39;symbol&#39;: &#39;EURUSDSP&#39;,</span>
<span class="go">    &#39;time&#39;: 1598922000100, &#39;best_bid&#39;: 1.19907, &#39;best_bid_amt&#39;: 94, &#39;best_ask&#39;: 1.19919,</span>
<span class="go">    &#39;best_ask_amt&#39;: 94, &#39;asks&#39;: [1.19919, 1.19925, 1.19931, 1.19937, 1.19943],</span>
<span class="go">    &#39;ask_vols&#39;: [94, 94, 94, 94, 94], &#39;bids&#39;: [1.19907, 1.19901, 1.19895, 1.19889, 1.19883],</span>
<span class="go">    &#39;bid_vols&#39;: [94, 94, 94, 94, 94], &#39;limit_up&#39;: &#39;&#39;, &#39;limit_down&#39;: &#39;&#39;}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.get_xswap_curve">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">get_xswap_curve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.get_xswap_curve" title="Link to this definition"></a></dt>
<dd><p>利率互换曲线查询</p>
<dl class="simple">
<dt>参数:</dt><dd><p>symbol(str):合约编码</p>
</dd>
</dl>
<p>Returns:dict</p>
<blockquote>
<div><p>{</p>
<blockquote>
<div><p>curveTenor(list(str)):关键期限</p>
<p>rateCurve(list(float)):原始曲线</p>
<p>curveDates(list(str)):关键期限点对应日期</p>
<p>spotRateCurve(list(float)):即期利率曲线</p>
<p>positiveSpotRateCurve(list(float)):正 即期利率曲线</p>
<p>negativeSpotRateCurve(list(float)):负 即期利率曲线</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">curve</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">get_xswap_curve</span><span class="p">(</span><span class="n">code</span><span class="o">=</span><span class="s1">&#39;FR007&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.inactiveBondPricing">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">inactiveBondPricing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.inactiveBondPricing" title="Link to this definition"></a></dt>
<dd><p>非活跃券定价</p>
<dl>
<dt>参数:</dt><dd><p>symbol(str):合约编码</p>
</dd>
<dt>返回:</dt><dd><p>netPrice(float):净价</p>
<p>dirtyPrice(float):全价</p>
<p>yieldToMaturity(float):收益率</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">netPrice</span><span class="p">,</span> <span class="n">dirtyPrice</span><span class="p">,</span> <span class="n">yieldToMaturity</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">inactiveBondPricing</span><span class="p">(</span><span class="s1">&#39;160017_T+1&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.put_maker_symbol">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">put_maker_symbol</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">floor_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.put_maker_symbol" title="Link to this definition"></a></dt>
<dd><p>获取做市合约以及floor_code信息</p>
<dl>
<dt>必填参数:</dt><dd><p>floor_code  str     floor_code (债券做市时,需要带RFQ字样)</p>
<p>symbol_list list    [做市合约的货币对(即通用合约的dealType字段)数组,*字符串*,不支持数字]</p>
</dd>
<dt>返回:</dt><dd><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_bar_spreads">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">query_bar_spreads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">spread_contract_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">ndarray</span><span class="p"><span class="pre">[</span></span><span class="pre">Any</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">dtype</span><span class="p"><span class="pre">[</span></span><span class="pre">_ScalarType_co</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="headerlink" href="#md.query_bar_spreads" title="Link to this definition"></a></dt>
<dd><p>价差Bar行情查询 无数据返回空numpy结构</p>
<dl>
<dt>参数:</dt><dd><p>spread_contract_name(str):价差合约名称</p>
<p>spread_contract_indicator(str): 价差指标名称</p>
<p>frequency(str): 查询频率 可输入 1N、5N、15N、30N、1H、2H、4H、1D、1W、1M</p>
<p>count(int): 查询数量 默认数量1</p>
</dd>
<dt>其他参数:</dt><dd><p>df      bool     是否返回 dataframe格式(default: {False})</p>
</dd>
<dt>返回:</dt><dd><p>价差Bar矩阵信息</p>
<p>Returns:nparr</p>
<dl>
<dt>[{</dt><dd><p>time(int): 时间戳</p>
<p>open(float): 开盘价</p>
<p>close(float): 收盘价</p>
<p>high(float): 最高价</p>
<p>low(float): 最低价</p>
<p>strat(int): bar的开始时间</p>
<p>end(int): bar的结束时间</p>
</dd>
</dl>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">barnp</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">query_bar_spreads</span><span class="p">(</span><span class="s2">&quot;价差合约1&quot;</span><span class="p">,</span> <span class="s2">&quot;价差指标1&quot;</span><span class="p">,</span> <span class="s2">&quot;1N&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_bars">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">query_bars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.query_bars" title="Link to this definition"></a></dt>
<dd><p>获取一段时间获取N根bar数据</p>
<dl>
<dt>必填参数:</dt><dd><p>symbol  str     合约唯一代码</p>
<p>type    str     数据类型</p>
<p>source  str     行情来源</p>
<p>count   int     bar数量</p>
</dd>
<dt>其他参数:</dt><dd><p>fields  list     指定返回对象字段(default: {None})</p>
<p>df      bool     是否返回 dataframe格式(default: {False})</p>
</dd>
<dt>返回:</dt><dd><p>Bar对象    list&lt;dict&gt;</p>
<p>Returns:list
[{</p>
<blockquote>
<div><p>time(int):时间戳</p>
<p>open(float):开盘价</p>
<p>close(float):收盘价</p>
<p>high(float):最高价</p>
<p>low(float):最低价</p>
<p>strat(int):bar的开始时间</p>
<p>end(int):bar的结束时间</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">query_bars</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="n">type_</span><span class="o">=</span><span class="s2">&quot;5N_BAR_ODM_DEPTH&quot;</span><span class="p">,</span> <span class="n">source</span><span class="o">=</span><span class="s2">&quot;CFETS_LC&quot;</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;open&quot;</span><span class="p">,</span> <span class="s2">&quot;close&quot;</span><span class="p">])</span>
<span class="go">    [{&#39;time&#39;: 1532017800200, &#39;start&#39;: 1532018100000.0, &#39;end&#39;: 1532018400000.0,</span>
<span class="go">    &#39;open&#39;: 1.1648800000000001, &#39;close&#39;: 1.16487, &#39;high&#39;: 1.1648800000000001, &#39;low&#39;: 1.1648649999999998}]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_bars_pro">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">query_bars_pro</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_datatime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#md.query_bars_pro" title="Link to this definition"></a></dt>
<dd><p>获取一段时间获取N根bar数据</p>
<dl>
<dt>必填参数:</dt><dd><p>symbol  str     合约唯一代码</p>
<p><a href="#id1"><span class="problematic" id="id2">type_</span></a>    str     数据类型</p>
<p>source  str     行情来源</p>
</dd>
<dt>其他参数:</dt><dd><p>count   int      bar数量(default: {-1}), 表示全部返回</p>
<p>fields  list     指定返回对象字段(default: {None})</p>
<p>data_type int    数据返回类型 0 表示pandas结构 1 表示numpy结构 2 表示dict结构(default: {1})</p>
<p>start_datatime str bar的开始时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
<p>end_datatime str bar的结束时间  支持以下格式’%Y-%m-%d %H:%M’, ‘%Y-%m-%d %H:%M:%S’, ‘%Y%m%d%H%M%S’</p>
</dd>
<dt>返回:</dt><dd><p>Bar数据 数据类型根据入参的data_type值而变</p>
<p>time(int):时间戳</p>
<p>open(float):开盘价</p>
<p>close(float):收盘价</p>
<p>high(float):最高价</p>
<p>low(float):最低价</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">quantapi.enums</span><span class="w"> </span><span class="kn">import</span> <span class="n">DATA_TYPE_PANDAS</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">bars</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">query_bars_pro</span><span class="p">(</span><span class="s2">&quot;160017_T+1&quot;</span><span class="p">,</span> <span class="s2">&quot;1N_BAR_DEPTH&quot;</span><span class="p">,</span> <span class="s2">&quot;X-BOND_HO&quot;</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">start_datatime</span><span class="o">=</span><span class="s2">&quot;2021-12-10 00:00&quot;</span><span class="p">,</span> <span class="n">end_datatime</span><span class="o">=</span><span class="s2">&quot;2024-12-20 00:00&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_coupons_pool_bars">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">query_coupons_pool_bars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coupon_pool_code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_termtomaturity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_termtomaturity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#md.query_coupons_pool_bars" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>获取券池日Bar</p>
</div></blockquote>
<dl>
<dt>必填参数:</dt><dd><p>coupon_pool_code(str)       券池编码</p>
<p>source(str)                 行情来源</p>
</dd>
<dt>其他参数:</dt><dd><p>count(int)                  查询条目数,-1查询所有 (default: {-1})</p>
<p>start_date(int)             起始时间,非必输</p>
<p>end_date(int)               结束时间,非必输</p>
<p>start_termtomaturity(int)   待偿期起始,非必输</p>
<p>end_termtomaturity(int)     待偿期结束,非必输</p>
</dd>
<dt>返回:</dt><dd><dl>
<dt>Returns:DataFrame</dt><dd><p>time(int):时间戳</p>
<p>open(float):开盘价</p>
<p>close(float):收盘价</p>
<p>high(float):最高价</p>
<p>low(float):最低价</p>
<p>strat(int):bar的开始时间</p>
<p>end(int):bar的结束时间</p>
<p>bond_code(str):债券编码</p>
</dd>
</dl>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">md</span><span class="o">.</span><span class="n">query_coupons_pool_bars</span><span class="p">(</span><span class="s2">&quot;国债&quot;</span><span class="p">,</span> <span class="n">source</span><span class="o">=</span><span class="s2">&quot;X-BOND_HO&quot;</span><span class="p">,</span> <span class="n">count</span><span class="o">=-</span><span class="mi">1</span><span class="p">,</span><span class="n">start_date</span><span class="o">=</span><span class="mi">20200101</span><span class="p">,</span> <span class="n">end_date</span><span class="o">=</span><span class="mi">20240101</span><span class="p">,</span> <span class="n">start_termtomaturity</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">end_termtomaturity</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="md.query_spreads">
<span class="sig-prename descclassname"><span class="pre">固收.md.</span></span><span class="sig-name descname"><span class="pre">query_spreads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">spread_contract_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_time</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_time</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">df</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">ndarray</span><span class="p"><span class="pre">[</span></span><span class="pre">Any</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">dtype</span><span class="p"><span class="pre">[</span></span><span class="pre">_ScalarType_co</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="headerlink" href="#md.query_spreads" title="Link to this definition"></a></dt>
<dd><p>价差Tick行情查询 无数据返回空numpy结构</p>
<dl>
<dt>参数:</dt><dd><p>spread_contract_name(str):价差合约名称</p>
<p>spread_contract_indicator(str): 价差指标名称</p>
<p>start_time(int): 查询起始时间</p>
<p>end_time(int): 查询结束时间</p>
</dd>
<dt>其他参数:</dt><dd><p>df      bool     是否返回 dataframe格式(default: {False})</p>
</dd>
<dt>返回:</dt><dd><p>价差Tick矩阵信息</p>
<p>Returns:nparr</p>
<dl>
<dt>[{</dt><dd><p>time(int): 价差时间戳</p>
<p>value(int): 价差值</p>
<p>status(int): 价差状态值</p>
</dd>
</dl>
<p>}]</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">arrnp</span> <span class="o">=</span> <span class="n">md</span><span class="o">.</span><span class="n">query_spreads</span><span class="p">(</span><span class="s2">&quot;价差合约1&quot;</span><span class="p">,</span> <span class="s2">&quot;价差指标1&quot;</span><span class="p">,</span> <span class="mi">1611681075394</span><span class="p">,</span> <span class="mi">1711681075394</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="deal.html" class="btn btn-neutral float-left" title="订单API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="pos.html" class="btn btn-neutral float-right" title="持仓API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>