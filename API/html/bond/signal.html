<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>信号API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="外汇API" href="../fx/index.html" />
    <link rel="prev" title="做市API" href="maker.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html">固收API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3"><a class="reference internal" href="md.html">行情API</a></li>
<li class="toctree-l3"><a class="reference internal" href="pos.html">持仓API</a></li>
<li class="toctree-l3"><a class="reference internal" href="maker.html">做市API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">信号API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#signal.to_signal"><code class="docutils literal notranslate"><span class="pre">to_signal()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../fx/index.html">外汇API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">固收API</a></li>
      <li class="breadcrumb-item active">信号API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/bond/signal.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.signal">
<span id="api"></span><h1>信号API<a class="headerlink" href="#module-.signal" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="signal.to_signal">
<span class="sig-prename descclassname"><span class="pre">固收.signal.</span></span><span class="sig-name descname"><span class="pre">to_signal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">side</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ytm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quantity</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">effect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">in_out_market</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">channel_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time_in_force</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expire_time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hedge_flag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'1'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">intention</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">warn_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop_price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maturity_date</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close_order_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">currency</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bond_quote_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">9</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#signal.to_signal" title="Link to this definition"></a></dt>
<dd><p>下发新的信号</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>symbol</strong> (<em>str</em>) – 合约唯一代码</p></li>
<li><p><strong>side</strong> (<em>str</em>) – 交易方向        B:买 S:卖</p></li>
<li><p><strong>price</strong> (<em>double</em>) – 报单价格</p></li>
<li><p><strong>ytm</strong> (<em>double</em>) – 到期收益率(%)</p></li>
<li><p><strong>quantity</strong> (<em>double</em>) – 报单总数量</p></li>
<li><p><strong>effect</strong> (<em>int</em>) – 开平仓类型 0:中性 1:开仓 2:平仓</p></li>
<li><p><strong>channel_code</strong> (<em>str</em>) – 交易渠道,只支持X-BOND市场</p></li>
<li><p><strong>pos_type</strong> (<em>str</em>) – 逐笔标识 0:不开启 1:开启</p></li>
<li><p><strong>frequency</strong> (<em>str</em>) – 时间框架(频率) 值如下:”1N”, “5N”, “15N”, “30N”, “1H”, “2H”, “4H”, “1D”, “1W”, “1M”</p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>hedge_flag</strong> (<em>str</em>) – 投机套保标识(default: {“1”})</p></li>
<li><p><strong>intention</strong> (<em>str</em>) – 交易意图(default: {None})</p></li>
<li><p><strong>warn_price</strong> (<em>double</em>) – 止损预警机价(default: {None})</p></li>
<li><p><strong>in_out_market</strong> (<em>int</em>) – 内外部市场(default: {2})</p></li>
<li><p><strong>time_in_force</strong> (<em>str</em>) – 订单时效性 (default: {1})</p></li>
<li><p><strong>channel_code</strong> (<em>str</em>) – 交易渠道 (default: {None})</p></li>
<li><p><strong>description</strong> (<em>str</em>) – 描述 (default: {‘’})</p></li>
<li><p><strong>expire_time</strong> (<em>str</em>) – 订单有效超时时间(default: {None})</p></li>
<li><p><strong>stop_price</strong> (<em>double</em>) – 止损价(default: {None})</p></li>
<li><p><strong>value_date</strong> (<em>str</em>) – 起息日(default: {None})</p></li>
<li><p><strong>maturity_date</strong> (<em>str</em>) – 到期日(default: {None})</p></li>
<li><p><strong>close_order_id</strong> (<em>str</em>) – 逐笔模式平仓id(default: {None})</p></li>
<li><p><strong>currency</strong> (<em>str</em>) – 货币类型(default: {None})</p></li>
<li><p><strong>bond_quote_type</strong> (<em>str</em>) – 报价方式(default: {9})</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>str: 信号ID号</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">id</span> <span class="o">=</span> <span class="n">signal</span><span class="o">.</span><span class="n">to_signal</span><span class="p">(</span>
<span class="go">            symbol=data[0][&#39;symbol&#39;],</span>
<span class="go">            side=&#39;B&#39;,</span>
<span class="go">            channel_code=&#39;X-BOND_HO&#39;,</span>
<span class="go">            price=data[0][&#39;best_bid&#39;],</span>
<span class="go">            frequency=&#39;1N&#39;,</span>
<span class="go">            time_in_force=&#39;4&#39;,</span>
<span class="go">            pos_type=0,</span>
<span class="go">            quantity=data[0][&#39;best_bid_amt&#39;],</span>
<span class="go">            effect=0,</span>
<span class="go">            in_out_market=2,</span>
<span class="go">            ytm=1)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="maker.html" class="btn btn-neutral float-left" title="做市API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../fx/index.html" class="btn btn-neutral float-right" title="外汇API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>