<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>做市API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=beaddf03"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="信号API" href="signal.html" />
    <link rel="prev" title="持仓API" href="pos.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../public.html">公共方法</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../trade_method.html">交易方法</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html">固收API</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deal.html">订单API</a></li>
<li class="toctree-l3"><a class="reference internal" href="md.html">行情API</a></li>
<li class="toctree-l3"><a class="reference internal" href="pos.html">持仓API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">做市API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_quote"><code class="docutils literal notranslate"><span class="pre">to_quote()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_quote_cancel"><code class="docutils literal notranslate"><span class="pre">to_quote_cancel()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_rfq_quote"><code class="docutils literal notranslate"><span class="pre">to_rfq_quote()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_rfq_quote_cancel"><code class="docutils literal notranslate"><span class="pre">to_rfq_quote_cancel()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_rfq_quote_order_confirm"><code class="docutils literal notranslate"><span class="pre">to_rfq_quote_order_confirm()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#maker.to_rfq_quote_rej"><code class="docutils literal notranslate"><span class="pre">to_rfq_quote_rej()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="signal.html">信号API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../fx/index.html">外汇API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../xrisk.html">风控API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../trade_method.html">交易方法</a></li>
          <li class="breadcrumb-item"><a href="index.html">固收API</a></li>
      <li class="breadcrumb-item active">做市API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/bond/maker.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-.maker">
<span id="api"></span><h1>做市API<a class="headerlink" href="#module-.maker" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="maker.to_quote">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_quote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maker_quote_depths</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">floor_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expire_time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_quote" title="Link to this definition"></a></dt>
<dd><p>LP目前仅支持以分层带量的方式提供市场行情数据,分层带量的报价形式分快照更新和增量更新两种
在分层带量报价簿中，每层市场数据的量和价格满足一定的关系，例如:VWAP
一笔交易仅可和一个分层带量报价的一层提交。LC可以和不同的LP的分层带量数据成交</p>
<dl>
<dt>债券做市</dt><dd><dl>
<dt>参数:</dt><dd><p>symbol(string):合约唯一代码</p>
<p>maker_quote_depths(list[Dict]):报价字段列表</p>
<blockquote>
<div><p>bid(float):买净价</p>
<p>bidAmt(float):买量</p>
<p>ask(float):卖净价</p>
<p>askAmt(float):卖量</p>
<p>bidYtm(float):买收益率</p>
<p>askYtm(float):卖收益率</p>
<p>bidStrikeYield(float):行权 买收益率</p>
<p>askStrikeYield(float):行权 卖收益率</p>
</div></blockquote>
<p>floor_code枚举  格式${渠道}-${报价类型} 默认 LP_ESP1</p>
<blockquote>
<div><p>CFETS-IND:交易中心,银行间,指示性报价</p>
<p>BC-IND:债券通指示性报价</p>
<p>CFETS-ESP:交易中心,银行间,ESP报价</p>
<p>CFAE-ESP:交易中心,银行间,ESP报价</p>
</div></blockquote>
</dd>
</dl>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker_quote_depths</span> <span class="o">=</span> <span class="p">[{</span><span class="s1">&#39;level&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;bid&#39;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span> <span class="s1">&#39;bidAmt&#39;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span> <span class="s1">&#39;ask&#39;</span><span class="p">:</span> <span class="mi">101</span><span class="p">,</span> <span class="s1">&#39;askAmt&#39;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span> <span class="s1">&#39;bidYtm&#39;</span><span class="p">:</span> <span class="mf">2.3</span><span class="p">,</span> <span class="s1">&#39;askYtm&#39;</span><span class="p">:</span><span class="mf">2.2</span><span class="p">},</span>
<span class="go">                          {&#39;level&#39;: 2, &#39;bid&#39;: 99, &#39;bidAmt&#39;: 200, &#39;ask&#39;: 102, &#39;askAmt&#39;: 200, &#39;bidYtm&#39;: 2.3, &#39;askYtm&#39;:2.2},</span>
<span class="go">                          {&#39;level&#39;: 3, &#39;bid&#39;: 98, &#39;bidAmt&#39;: 300, &#39;ask&#39;: 103, &#39;askAmt&#39;: 300, &#39;bidYtm&#39;: 2.3, &#39;askYtm&#39;:2.2}]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_quote</span><span class="p">(</span><span class="s2">&quot;160017_T+1&quot;</span><span class="p">,</span> <span class="n">maker_quote_depths</span><span class="p">,</span> <span class="s2">&quot;LP_ESP1&quot;</span><span class="p">,</span> <span class="n">expire_time</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="maker.to_quote_cancel">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_quote_cancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">floor_code</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_quote_cancel" title="Link to this definition"></a></dt>
<dd><p>撤销一个报价并不终止做市方的报价流。</p>
<dl>
<dt>必填参数:</dt><dd><p>symbol(string):合约唯一代码</p>
</dd>
<dt>其他参数:</dt><dd><p>floor_code(string):交易分组 (default: {LP_ESP1}当策略只有一个floor_code，且其在策略参数配置唯一配置，则无需输入)</p>
</dd>
<dt>返回:</dt><dd><p>无</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_quote_cancel</span><span class="p">(</span><span class="s2">&quot;160017_T+1&quot;</span><span class="p">,</span> <span class="s2">&quot;LP_ESP1&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="maker.to_rfq_quote">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_rfq_quote</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reqId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bidSpot</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">askSpot</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bidPoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">askPoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">farBidPoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">farAskPoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ytm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strikeYield</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">orderQty</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">validTime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_rfq_quote" title="Link to this definition"></a></dt>
<dd><p>做市方使用to_rfq_quote报文响应taker的onRfq请求。 rfqQuote报文用 QuoteReqID 与 onRfq 报文关联对应，每次报价被修改时， 唯一的QuoteId也会被更新。
报价可以是单个方向或两个方向的报价。允许发送一个方向的报价来响应两个方向的报价请求。 这意味着在另一个方向上没有价格。
对于单个有效的 RFQ ，每个做市方最多只有一个有效报价。对于同一个 QuoteReqlD 的后续报价将会覆盖之前的报价（所有方向）, to_rfq_quote_cancel函数不是必须的，除非在未发送新的报价之前想要撤销该报价。
报价是有有效时间的，到了该时刻报价将失效。报价过期之后没有必要发送 to_rfq_quote_cancel 函数进行撤销。</p>
<dl>
<dt>外汇做市</dt><dd><dl>
<dt>必填参数:</dt><dd><p>reqId(string):询价报价请求Id</p>
</dd>
<dt>其他参数:</dt><dd><p>bidSpot(float):买入即期价格 (远期和掉期时为即期市场价格)</p>
<p>askSpot(float):卖出即期价格 (远期和掉期时为即期市场价格)</p>
<p>bidPoints(float):近端买入升贴水 (default: {None})远期和掉期时必选</p>
<p>askPoints(float):近端卖出升贴水 (default: {None})远期和掉期时必选</p>
<p>farBidPoints(float):远端买入升贴水 (default: {None})掉期时必选</p>
<p>farAskPoints(float):远端卖出升贴水 (default: {None})掉期时必选</p>
</dd>
</dl>
</dd>
<dt>债券做市:</dt><dd><p>reqId(string):询价报价请求Id</p>
<p>price(float Y): 净价 default: {None}</p>
<p>ytm(float): 收益率 default: {None}</p>
<p>strikeYield(float): 行权收益率 default: {None}</p>
<p>orderQty(float): 请求报价回复量 default: {None}</p>
<p>validTime(string): 报价有效时间YYYYMMDDHHmmss default: {None} 默认两小时</p>
</dd>
<dt>返回:</dt><dd><p>无</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_rfq_quote</span><span class="p">(</span><span class="mi">216867660628103168</span><span class="p">,</span> <span class="mf">7.1</span><span class="p">,</span> <span class="mf">7.2</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="maker.to_rfq_quote_cancel">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_rfq_quote_cancel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req_id</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_rfq_quote_cancel" title="Link to this definition"></a></dt>
<dd><p>做市方通过执行to_rfq_quote_cancel函数来撤销报价。
做市方发送给CFETS每个报价将覆盖先前同一req_id的报价。 此外，报价将根据域 ValldUntilTime设定的报价有效时间自动过期。
如果做市方希望撤销一个有效报价而不发送报价更新，做市方可以执行to_rfq_quote_cancel函数， req_id和quote_id对应的报文将被撤销。
撤销一个报价并不终止做市方的报价流。做市方执行 to_rfq_quote_cancel函数后仍然可以重新发送报价， 只要RFQ没有过期。</p>
<dl>
<dt>必填参数:</dt><dd><p>req_id(string):询价报价请求Id</p>
</dd>
<dt>返回:</dt><dd><p>无</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_rfq_quote_cancel</span><span class="p">(</span><span class="mi">216867660628103168</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="maker.to_rfq_quote_order_confirm">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_rfq_quote_order_confirm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">order_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exec_type</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_rfq_quote_order_confirm" title="Link to this definition"></a></dt>
<dd><p>做市方发送订单确认给CFETS</p>
<dl>
<dt>必填参数:</dt><dd><p>order_id(string):订单唯一标识</p>
<p>exec_type(string):执行类型8:订单拒绝 F:成交</p>
</dd>
<dt>返回:</dt><dd><p>无</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_rfq_quote_order_confirm</span><span class="p">(</span><span class="mi">216867660628103168</span><span class="p">,</span> <span class="s2">&quot;F&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="maker.to_rfq_quote_rej">
<span class="sig-prename descclassname"><span class="pre">固收.maker.</span></span><span class="sig-name descname"><span class="pre">to_rfq_quote_rej</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">quote_req_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reject_reason</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reject_text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="headerlink" href="#maker.to_rfq_quote_rej" title="Link to this definition"></a></dt>
<dd><p>对于做市方不希望参与的RFQ,可发送to_rfq_quote_rej 函数来拒绝CFETS的报价请求。</p>
<dl>
<dt>必填参数:</dt><dd><p>quote_req_id(string):订单唯一标识</p>
<p>reject_reason(string):拒绝原因</p>
<blockquote>
<div><p>1 =无效的产品要素</p>
<p>2 =该产品当前报价无效</p>
<p>3 =报价请求数量超过了限制</p>
<p>4 = LP已结束交易时间</p>
<p>5 =无效的价格</p>
<p>6 =该请求未得到Maker的授权</p>
<p>7 =无匹配项（No Match For Inquiry）</p>
<p>8 = RFQ不支持这个产品</p>
<p>9 =流动性不足（No Inventory）</p>
<p>10 = Pass由于市场条件Maker不愿参与RFQ</p>
<p>11 =代理方授信额度不足以完成交易</p>
<p>99 =其他</p>
</div></blockquote>
</dd>
<dt>其他参数:</dt><dd><p>reject_text(string):描述具体的拒绝原因 (default: {None} 当reject_reason为99时, 需说明决绝原因)</p>
</dd>
<dt>返回:</dt><dd><p>无</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">maker</span><span class="o">.</span><span class="n">to_rfq_quote_rej</span><span class="p">(</span><span class="mi">216867660628103168</span><span class="p">,</span> <span class="s2">&quot;1&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="pos.html" class="btn btn-neutral float-left" title="持仓API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="signal.html" class="btn btn-neutral float-right" title="信号API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>