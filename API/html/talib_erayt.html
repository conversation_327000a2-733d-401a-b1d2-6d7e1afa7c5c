<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>指标 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="prev" title="MT4API" href="mt4.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">指标</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.CCI"><code class="docutils literal notranslate"><span class="pre">CCI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.KELCHAN"><code class="docutils literal notranslate"><span class="pre">KELCHAN()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.MA"><code class="docutils literal notranslate"><span class="pre">MA()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.MACD"><code class="docutils literal notranslate"><span class="pre">MACD()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.MFI"><code class="docutils literal notranslate"><span class="pre">MFI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.MOM"><code class="docutils literal notranslate"><span class="pre">MOM()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.OBV"><code class="docutils literal notranslate"><span class="pre">OBV()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.RSI"><code class="docutils literal notranslate"><span class="pre">RSI()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#talib_erayt.STOCH"><code class="docutils literal notranslate"><span class="pre">STOCH()</span></code></a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">指标</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/talib_erayt.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-talib_erayt">
<span id="id1"></span><h1>指标<a class="headerlink" href="#module-talib_erayt" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.CCI">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">CCI</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">high</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">low</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">14</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.CCI" title="Link to this definition"></a></dt>
<dd><p>顺势指标</p>
<dl>
<dt>必选参数 :</dt><dd><p>high               ndarray             最高价序列</p>
<p>low                ndarray             最低价序列</p>
<p>close              ndarray             收盘价序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>timeperiod         int                 时间窗口(默认值 :14)</p>
</dd>
<dt>返回 :</dt><dd><p>顺势指标列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">CCI</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span>
<span class="go">            np.array([1., 2., 3., 4., 5., 6., 7., 8., 9., 10.]), timeperiod=5)</span>
<span class="go">    [nan nan  nan nan 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.KELCHAN">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">KELCHAN</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">high</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">low</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cal_factor</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ma_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'EMA'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.KELCHAN" title="Link to this definition"></a></dt>
<dd><p>镜像指标</p>
<dl>
<dt>必选参数 :</dt><dd><p>high               ndarray             最高价序列</p>
<p>low                ndarray             最低价序列</p>
<p>close              ndarray             收盘价序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>timeperiod         int                 时间窗口(默认值 :20)</p>
<p>cal_factor         int                 计算因子(默认值 :1)</p>
<p>ma_type            str                 ma类型(默认值 :”EMA”)</p>
</dd>
<dt>返回 :</dt><dd><p>上轨、中轨和下轨三个结果</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">KELCHAN</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span>
<span class="go">            np.array([1., 2., 3., 4., 5., 6., 7., 8., 9., 10.]), timeperiod=5)</span>
<span class="go">    [nan nan  nan nan 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111]</span>
</pre></div>
</div>
</dd>
<dt>异常：</dt><dd><p>执行异常会抛出QuantErrorException异常</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.MA">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">MA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">se</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">14</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span></span></span><a class="headerlink" href="#talib_erayt.MA" title="Link to this definition"></a></dt>
<dd><p>移动平均线
移动平均线是技术分析理论中应用最普遍的指标之一，主要用于确认、跟踪和判断趋势，提示买入和卖出信号，
在单边市场行情中可以较好的把握市场机会和规避风险。但是，移动平均线一般要与其他的技术指标或基本面相结合来使用，
特别是当市场处于盘整行情时，其买入卖出信号会频繁出现，容易失真。</p>
<dl>
<dt>必选参数 :</dt><dd><p>se          ndarray           价格序列</p>
<p>timeperiod  int             时间窗口(默认值 :14)</p>
</dd>
<dt>返回 :</dt><dd><p>移动平均值列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">MA</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">timeperiod</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
<span class="go">    [nan nan nan nan  3.  4.  5.  6.  7.  8.]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.MACD">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">MACD</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">se</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fastperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">12</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">slowperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">26</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">signalperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">9</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.MACD" title="Link to this definition"></a></dt>
<dd><p>指数平滑移动平均线</p>
<dl>
<dt>必选参数 :</dt><dd><p>se              ndarray             价格序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>fastperiod      int                 短周期(默认值 :12)</p>
<p>slowperiod      int                 长周期(默认值 :26)</p>
<p>signalperiod    int                 信号周期(默认值 :9)</p>
</dd>
<dt>返回 :</dt><dd><p>(macd,
macdsignal,
macdhist)</p>
</dd>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">MACD</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">fastperiod</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">slowperiod</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">signalperiod</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
<span class="go">    (array([nan, nan, nan, nan, nan, nan, nan,  1.,  1.,  1.]),</span>
<span class="go">     array([nan, nan, nan, nan, nan, nan, nan,  1.,  1.,  1.]),</span>
<span class="go">     array([nan, nan, nan, nan, nan, nan, nan,  0.,  0.,  0.]))</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.MFI">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">MFI</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">high</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">low</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">volume</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">14</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.MFI" title="Link to this definition"></a></dt>
<dd><p>货币流量指数</p>
<dl>
<dt>必选参数 :</dt><dd><p>high               ndarray             最高价序列</p>
<p>low                ndarray             最低价序列</p>
<p>close              ndarray             收盘价序列</p>
<p>volume             ndarray             成交量序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>timeperiod         int                 时间窗口(默认值 :14)</p>
</dd>
<dt>返回 :</dt><dd><p>货币流量指数列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">MFI</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span>
<span class="go">            np.array([1., 2., 3., 4., 5., 6., 7., 8., 9., 10.]), np.array([20., 30., 20., 20., 30., 20., 20., 30., 20., 30.]),</span>
<span class="go">            timeperiod=5)</span>
<span class="go">    [ nan  nan  nan  nan  nan 100. 100. 100. 100. 100.]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.MOM">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">MOM</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">se</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.MOM" title="Link to this definition"></a></dt>
<dd><p>动量</p>
<dl>
<dt>必选参数 :</dt><dd><p>se                 ndarray             价格序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>timeperiod         int                 时间窗口(默认值 :10)</p>
</dd>
<dt>返回 :</dt><dd><p>动量列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">MOM</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">timeperiod</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
<span class="go">    [nan nan nan nan nan  5.  5.  5.  5.  5.]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.OBV">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">OBV</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">se</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">volume</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.OBV" title="Link to this definition"></a></dt>
<dd><p>能量潮</p>
<dl>
<dt>必选参数 :</dt><dd><p>se                 ndarray             价格序列</p>
<p>volume             ndarray             成交量序列</p>
</dd>
<dt>返回 :</dt><dd><p>能量潮列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">OBV</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span>
<span class="go">            np.array([20., 30., 20., 20., 30., 20., 20., 30., 20., 30.]))</span>
<span class="go">    [ 20.  50.  70.  90. 120. 140. 160. 190. 210. 240.]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.RSI">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">RSI</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">se</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">14</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.RSI" title="Link to this definition"></a></dt>
<dd><p>相对强弱指数</p>
<dl>
<dt>必选参数 :</dt><dd><p>se                 ndarray             价格序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>timeperiod         int                 时间窗口(默认值 :14)</p>
</dd>
<dt>返回 :</dt><dd><p>相对强弱指数列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">RSI</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">timeperiod</span> <span class="o">=</span> <span class="mi">5</span><span class="p">)</span>
<span class="go">    [ nan  nan  nan  nan  nan 100. 100. 100. 100. 100.]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="talib_erayt.STOCH">
<span class="sig-prename descclassname"><span class="pre">talib_erayt.</span></span><span class="sig-name descname"><span class="pre">STOCH</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">high</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">low</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fastk_period</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">5</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">slowk_period</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">3</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">slowk_matype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">slowd_period</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">3</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">slowd_matype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#talib_erayt.STOCH" title="Link to this definition"></a></dt>
<dd><p>随机指标，俗称KD</p>
<dl>
<dt>必选参数 :</dt><dd><p>high               ndarray             最高价序列</p>
<p>low                ndarray             最低价序列</p>
<p>close              ndarray             收盘价序列</p>
</dd>
<dt>可选参数 :</dt><dd><p>fastk_period         int                 时间窗口(默认值 :5)</p>
<p>slowk_period         int                 时间窗口(默认值 :3)</p>
<p>slowk_matype         int                 时间窗口(默认值 :0)</p>
<p>slowd_period         int                 时间窗口(默认值 :3)</p>
<p>slowd_matype         int                 时间窗口(默认值 :0)</p>
</dd>
<dt>返回 :</dt><dd><p>随机指标列表，最后值为最新均值</p>
</dd>
<dt>使用方法 :</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">talib</span><span class="o">.</span><span class="n">STOCH</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">2.</span><span class="p">,</span> <span class="mf">3.</span><span class="p">,</span> <span class="mf">4.</span><span class="p">,</span> <span class="mf">5.</span><span class="p">,</span> <span class="mf">6.</span><span class="p">,</span> <span class="mf">7.</span><span class="p">,</span> <span class="mf">8.</span><span class="p">,</span> <span class="mf">9.</span><span class="p">,</span> <span class="mf">10.</span><span class="p">]),</span>
<span class="go">            np.array([1., 2., 3., 4., 5., 6., 7., 8., 9., 10.]), timeperiod=5)</span>
<span class="go">    [nan nan  nan nan 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111 111.11111111]</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="mt4.html" class="btn btn-neutral float-left" title="MT4API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>