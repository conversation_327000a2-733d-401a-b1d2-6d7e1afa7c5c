<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>指标API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="交易API" href="trade_arsenals.html" />
    <link rel="prev" title="武器库" href="arsenals_method.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="arsenals_method.html">武器库</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">指标API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.BULL_BEAR"><code class="docutils literal notranslate"><span class="pre">BULL_BEAR()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.ERAYTMA"><code class="docutils literal notranslate"><span class="pre">ERAYTMA()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.FibonacciMA"><code class="docutils literal notranslate"><span class="pre">FibonacciMA()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.GMMA"><code class="docutils literal notranslate"><span class="pre">GMMA()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.MACDBOLL"><code class="docutils literal notranslate"><span class="pre">MACDBOLL()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.PP_Camarilla"><code class="docutils literal notranslate"><span class="pre">PP_Camarilla()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.PP_DeMarks"><code class="docutils literal notranslate"><span class="pre">PP_DeMarks()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.PP_Fibonacci"><code class="docutils literal notranslate"><span class="pre">PP_Fibonacci()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.PP_Standard"><code class="docutils literal notranslate"><span class="pre">PP_Standard()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.PP_Woodie"><code class="docutils literal notranslate"><span class="pre">PP_Woodie()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#indicator_arsenals.TRADMA"><code class="docutils literal notranslate"><span class="pre">TRADMA()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="trade_arsenals.html">交易API</a></li>
<li class="toctree-l2"><a class="reference internal" href="mt4.html">MT4API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="arsenals_method.html">武器库</a></li>
      <li class="breadcrumb-item active">指标API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/indicator_arsenals.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-indicator_arsenals">
<span id="api"></span><h1>指标API<a class="headerlink" href="#module-indicator_arsenals" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.BULL_BEAR">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">BULL_BEAR</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.BULL_BEAR" title="Link to this definition"></a></dt>
<dd><p>获取牛熊标识</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> (<em>string</em>) – 牛熊参数名称 (仅订阅一个牛熊指标时可不传)</p>
</dd>
</dl>
<p>返回: 牛熊标识,0 - 未知; 1 - 熊; 2 - 牛; 3 - 已达可能熊; 4 - 已达可能牛</p>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">bull_bear</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">BULL_BEAR</span><span class="p">(</span><span class="s1">&#39;牛熊参数&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.ERAYTMA">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">ERAYTMA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.ERAYTMA" title="Link to this definition"></a></dt>
<dd><p>银通均线</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {100})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>EMA20(np.float64):指数移动均线周期20</p>
<p>EMA30(np.float64):指数移动均线周期30</p>
<p>EMA40(np.float64):指数移动均线周期40</p>
<p>EMA50(np.float64):指数移动均线周期50</p>
<p>EMA60(np.float64):指数移动均线周期60</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">eraytmas</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">ERAYTMA</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.FibonacciMA">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">FibonacciMA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">200</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.FibonacciMA" title="Link to this definition"></a></dt>
<dd><p>斐波那契均线</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {200})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>SMA5(np.float64):标准均线周期5</p>
<p>SMA8(np.float64):标准均线周期8</p>
<p>SMA13(np.float64):标准均线周期13</p>
<p>SMA21(np.float64):标准均线周期21</p>
<p>SMA34(np.float64):标准均线周期34</p>
<p>SMA55(np.float64):标准均线周期55</p>
<p>SMA89(np.float64):标准均线周期89</p>
<p>SMA144(np.float64):标准均线周期144</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">fibonaccimas</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_Woodie</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.GMMA">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">GMMA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.GMMA" title="Link to this definition"></a></dt>
<dd><p>顾比均线</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {100})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>EMA3(np.float64):指数移动均线周期3</p>
<p>EMA5(np.float64):指数移动均线周期5</p>
<p>EMA8(np.float64):指数移动均线周期8</p>
<p>EMA10(np.float64):指数移动均线周期10</p>
<p>EMA12(np.float64):指数移动均线周期12</p>
<p>EMA15(np.float64):指数移动均线周期15</p>
<p>EMA30(np.float64):指数移动均线周期30</p>
<p>EMA35(np.float64):指数移动均线周期35</p>
<p>EMA40(np.float64):指数移动均线周期40</p>
<p>EMA45(np.float64):指数移动均线周期45</p>
<p>EMA50(np.float64):指数移动均线周期50</p>
<p>EMA60(np.float64):指数移动均线周期60</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">gmmas</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">GMMA</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.MACDBOLL">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">MACDBOLL</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boll_timeperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">15</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boll_nbdevup</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boll_nbdevdn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">macd_fastperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">macd_slowperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">15</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">macd_signalperiod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.MACDBOLL" title="Link to this definition"></a></dt>
<dd><p>偏差带</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>取bar的数量</strong><strong>(</strong><strong>default</strong> (<em>bar_count  int</em>) – {10})</p></li>
<li><p><strong>计算周期</strong><strong>(</strong><strong>default</strong> (<em>boll_timeperiod      String</em>) – {15})</p></li>
<li><p><strong>上轨线价格相对于中轨线标准差倍数</strong><strong>(</strong><strong>default</strong> (<em>boll_nbdevup      String</em>) – {2})</p></li>
<li><p><strong>下轨线价格相对于中轨线标准差倍数</strong><strong>(</strong><strong>default</strong> (<em>boll_nbdevdn      String</em>) – {2})</p></li>
<li><p><strong>短周期均线</strong><strong>(</strong><strong>default</strong> (<em>macd_fastperiod      String</em>) – {6})</p></li>
<li><p><strong>长周期均线</strong><strong>(</strong><strong>default</strong> (<em>macd_slowperiod      String</em>) – {15})</p></li>
<li><p><strong>计算DEA天数</strong><strong>(</strong><strong>default</strong> (<em>macd_signalperiod      String</em>) – {2})</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>Returns:np.arr</p>
<blockquote>
<div><p>[0,1,-1]</p>
<ol class="arabic simple">
<li><p>当行情自下而上穿过布林带中线, 并且MACD大于0, 进场做多, 标记为1</p></li>
<li><p>当行情自上而下穿过布林带中线, 并且MACD小于0, 进场做空, 标记为-1</p></li>
<li><p>其余情况标记为0</p></li>
</ol>
</div></blockquote>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">marks</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">MACDBOLL</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">,</span> <span class="n">bar_count</span><span class="p">,</span> <span class="n">boll_timeperiod</span><span class="p">,</span> <span class="n">boll_nbdevup</span><span class="p">,</span> <span class="n">boll_nbdevdn</span><span class="p">,</span> <span class="n">macd_fastperiod</span><span class="p">,</span> <span class="n">macd_slowperiod</span><span class="p">,</span> <span class="n">macd_signalperiod</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.PP_Camarilla">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">PP_Camarilla</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.PP_Camarilla" title="Link to this definition"></a></dt>
<dd><p>卡玛里拉轴心点</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {10})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>R5(np.float64):阻力位5</p>
<p>R4(np.float64):阻力位4</p>
<p>R3(np.float64):阻力位3</p>
<p>R2(np.float64):阻力位2</p>
<p>R1(np.float64):阻力位1</p>
<p>S1(np.float64):支撑位1</p>
<p>S2(np.float64):支撑位2</p>
<p>S3(np.float64):支撑位3</p>
<p>S4(np.float64):支撑位4</p>
<p>S5(np.float64):支撑位5</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pivot_points</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_Camarilla</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.PP_DeMarks">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">PP_DeMarks</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.PP_DeMarks" title="Link to this definition"></a></dt>
<dd><p>德玛克斯轴心点</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {10})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>R1(np.float64):阻力位1</p>
<p>S1(np.float64):支撑位1</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pivot_points</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_DeMarks</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.PP_Fibonacci">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">PP_Fibonacci</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.PP_Fibonacci" title="Link to this definition"></a></dt>
<dd><p>斐波那契轴心点</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {10})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>R3(np.float64):阻力位3</p>
<p>R2(np.float64):阻力位2</p>
<p>R1(np.float64):阻力位1</p>
<p>PP(np.float64):轴心点</p>
<p>S1(np.float64):支撑位1</p>
<p>S2(np.float64):支撑位2</p>
<p>S3(np.float64):支撑位3</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pivot_points</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_Fibonacci</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.PP_Standard">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">PP_Standard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.PP_Standard" title="Link to this definition"></a></dt>
<dd><p>标准轴心点</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {10})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>R3(np.float64):阻力位3</p>
<p>R2(np.float64):阻力位2</p>
<p>R1(np.float64):阻力位1</p>
<p>PP(np.float64):轴心点</p>
<p>S1(np.float64):支撑位1</p>
<p>S2(np.float64):支撑位2</p>
<p>S3(np.float64):支撑位3</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pivot_points</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_Standard</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.PP_Woodie">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">PP_Woodie</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">field</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'close'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.PP_Woodie" title="Link to this definition"></a></dt>
<dd><p>伍迪轴心点</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>(</strong><strong>default</strong> (<em>field      String    基准价采样 close open</em>) – {10})</p></li>
<li><p><strong>(</strong><strong>default</strong> – {close})</p></li>
</ul>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>R2(np.float64):阻力位2</p>
<p>R1(np.float64):阻力位1</p>
<p>PP(np.float64):轴心点</p>
<p>S1(np.float64):支撑位1</p>
<p>S2(np.float64):支撑位2</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pivot_points</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">PP_Woodie</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="n">field</span><span class="o">=</span><span class="s1">&#39;close&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="indicator_arsenals.TRADMA">
<span class="sig-prename descclassname"><span class="pre">indicator_arsenals.</span></span><span class="sig-name descname"><span class="pre">TRADMA</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bar_count</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#indicator_arsenals.TRADMA" title="Link to this definition"></a></dt>
<dd><p>传统均线流</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>合约唯一代码</strong> (<em>symbol     str</em>) – </p></li>
<li><p><strong>渠道</strong> (<em>source     str</em>) – </p></li>
<li><p><strong>频率类型</strong> (<em>frequency  str</em>) – </p></li>
</ul>
</dd>
<dt class="field-even">参数<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>(</strong><strong>default</strong> (<em>bar_count  int  取bar的数量</em>) – {100})</p>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:np.arr</p>
<p>[{</p>
<p>SMA5(np.float64):标准均线周期5</p>
<p>SMA10(np.float64):标准均线周期10</p>
<p>SMA20(np.float64):标准均线周期20</p>
<p>SMA30(np.float64):标准均线周期30</p>
<p>SMA60(np.float64):标准均线周期60</p>
<p>}]</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tradmas</span> <span class="o">=</span> <span class="n">indicator_arsenals</span><span class="o">.</span><span class="n">TRADMA</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">frequency</span><span class="p">,</span> <span class="n">bar_count</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="arsenals_method.html" class="btn btn-neutral float-left" title="武器库" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="trade_arsenals.html" class="btn btn-neutral float-right" title="交易API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>