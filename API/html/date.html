<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>时间API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="日志API" href="qlog.html" />
    <link rel="prev" title="公共方法" href="public.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="public.html">公共方法</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">时间API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#date.get_bus_day"><code class="docutils literal notranslate"><span class="pre">get_bus_day()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#date.get_bus_time"><code class="docutils literal notranslate"><span class="pre">get_bus_time()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#date.get_open_close_market"><code class="docutils literal notranslate"><span class="pre">get_open_close_market()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#date.get_sys_time"><code class="docutils literal notranslate"><span class="pre">get_sys_time()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#date.get_timestamp"><code class="docutils literal notranslate"><span class="pre">get_timestamp()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="qlog.html">日志API</a></li>
<li class="toctree-l2"><a class="reference internal" href="funds.html">资金账号</a></li>
<li class="toctree-l2"><a class="reference internal" href="param.html">配置参数</a></li>
<li class="toctree-l2"><a class="reference internal" href="scheduler.html">定时任务</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="public.html">公共方法</a></li>
      <li class="breadcrumb-item active">时间API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/date.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-date">
<span id="api"></span><h1>时间API<a class="headerlink" href="#module-date" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="date.get_bus_day">
<span class="sig-prename descclassname"><span class="pre">date.</span></span><span class="sig-name descname"><span class="pre">get_bus_day</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#date.get_bus_day" title="Link to this definition"></a></dt>
<dd><p>获取某个合约的业务时间</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>symbol</strong> (<em>string</em>) – 业务类型，输入合约编码</p>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>日期(int): 返回业务日期 如: 20190121</p>
</dd>
<dt>使用方法：</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_bus_day</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="date.get_bus_time">
<span class="sig-prename descclassname"><span class="pre">date.</span></span><span class="sig-name descname"><span class="pre">get_bus_time</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">channel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#date.get_bus_time" title="Link to this definition"></a></dt>
<dd><p>获取某个渠道的交易时段信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>channel</strong> (<em>string</em>) – 交易渠道</p>
</dd>
</dl>
<dl>
<dt>返回：</dt><dd><p>Returns:list
[{</p>
<blockquote>
<div><p>code(string):交易渠道</p>
<p>type(string):类型 [DAY:全天 AM:上午 PM:下午 VESP:夜间]</p>
<p>name(string):英文名称</p>
<p>localName(string):本地名称</p>
<p>startTime(string):开始时间 (格式 09:00:00)</p>
<p>endTime(string):结束时间 (格式 11:30:00)</p>
<p>adjustType(string):调整类型 [A:添加 D:排除]</p>
<p>timeStamp(string):修改时间</p>
<p>comments(string):描述</p>
<p>marketStatus(string):市场状态 [OC:开始/闭市 OR:开始/休市 S:停市 PR:暂停/恢复交易]</p>
<p>tradePeriodGroup(string):日期类型 [C:通用 S:特殊]</p>
<p>tradePeriodTime(string):适用时间 (1,2,3,4,5 以数字表示周几多个以,区分)</p>
</div></blockquote>
<p>}]</p>
</dd>
<dt>使用方法：</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_bus_time</span><span class="p">(</span><span class="s2">&quot;UBS_HO&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="date.get_open_close_market">
<span class="sig-prename descclassname"><span class="pre">date.</span></span><span class="sig-name descname"><span class="pre">get_open_close_market</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">market</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">query_time</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#date.get_open_close_market" title="Link to this definition"></a></dt>
<dd><p>获取查询时间所在的开闭市时间段</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>symbol</strong> (<em>string</em>) – 合约编码，输入合约编码</p></li>
<li><p><strong>market</strong> (<em>string</em>) – 交易市场，输入交易市场编码，例：UBS</p></li>
<li><p><strong>query_time</strong> (<em>int</em>) – 查询时间，输入要查询的时间戳，毫秒级，不传则默认用当前行情时间</p></li>
</ul>
</dd>
</dl>
<dl>
<dt>返回:</dt><dd><p>开市时间，闭市时间，返回数组</p>
</dd>
<dt>使用方法：</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_open_close_market</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="s2">&quot;UBS&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_open_close_market</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">,</span> <span class="s2">&quot;UBS&quot;</span><span class="p">,</span> <span class="mi">20221201</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="date.get_sys_time">
<span class="sig-prename descclassname"><span class="pre">date.</span></span><span class="sig-name descname"><span class="pre">get_sys_time</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#date.get_sys_time" title="Link to this definition"></a></dt>
<dd><p>获取当前的系统时间</p>
<dl>
<dt>返回:</dt><dd><p>time: 返回系统时间 如: 20190128121103</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_sys_time</span><span class="p">()</span>
<span class="go">&quot;20190128121103&quot;</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="date.get_timestamp">
<span class="sig-prename descclassname"><span class="pre">date.</span></span><span class="sig-name descname"><span class="pre">get_timestamp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="headerlink" href="#date.get_timestamp" title="Link to this definition"></a></dt>
<dd><p>返回当前策略时间的毫秒级时间戳</p>
<dl>
<dt>返回:</dt><dd><p>返回当前策略时间的毫秒级时间戳(int类型)</p>
</dd>
<dt>使用方法：</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">date</span><span class="o">.</span><span class="n">get_timestamp</span><span class="p">()</span>
<span class="go">    1705632663000</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="public.html" class="btn btn-neutral float-left" title="公共方法" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="qlog.html" class="btn btn-neutral float-right" title="日志API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>