<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>基础API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="公共方法" href="public.html" />
    <link rel="prev" title="快速开始" href="quick_start.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">基础API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#base.get_bond_cash_flow"><code class="docutils literal notranslate"><span class="pre">get_bond_cash_flow()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#base.get_bond_info"><code class="docutils literal notranslate"><span class="pre">get_bond_info()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#base.get_bull_bear_flag"><code class="docutils literal notranslate"><span class="pre">get_bull_bear_flag()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#base.get_contract"><code class="docutils literal notranslate"><span class="pre">get_contract()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#base.query_coupon_pool_bond_info"><code class="docutils literal notranslate"><span class="pre">query_coupon_pool_bond_info()</span></code></a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">基础API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/base.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-base">
<span id="api"></span><h1>基础API<a class="headerlink" href="#module-base" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="base.get_bond_cash_flow">
<span class="sig-prename descclassname"><span class="pre">base.</span></span><span class="sig-name descname"><span class="pre">get_bond_cash_flow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bond_code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#base.get_bond_cash_flow" title="Link to this definition"></a></dt>
<dd><p>获取债券现金流信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>bond_code</strong> (<em>string</em>) – 债券编码</p>
</dd>
</dl>
<p>Returns:list
[{</p>
<blockquote>
<div><p>key(string):债券现金流主键</p>
<p>bondsId(int):债券编码id</p>
<p>cashflowNo(int):债券编码现金流编码</p>
<p>currency(string):货币</p>
<p>cashflowType(string):现金流类型P-Pemium, R-Principal, I-Interest, F-Fee</p>
<p>cashflowStatus(string):现金流状态 I-Init，F-Fixing</p>
<p>dealType(string):收付方向 P-Pay R-Receive</p>
<p>cashflowDate(int):债券现金流日期</p>
<p>cashflowTime(int):债券现金流时间</p>
<p>amount(float):金额</p>
<p>notional(float):本金</p>
<p>paymentDate(int):付款日期</p>
<p>exDivideneDate(int):除权日期</p>
<p>adjustDate(string):是否调整日期 N-No Y-Yes</p>
<p>startDate(int):开始时间</p>
<p>endDate(int):结束时间</p>
<p>startDateAdj(int):报价单位</p>
<p>endDateAdj(int):开始交易日</p>
<p>period(int):周期</p>
<p>phase(string):阶段 B-期初 M-期中 E-期末</p>
<p>basis(string):计息基础</p>
<p>auditTimeStamp(string):时间戳</p>
<p>resets(string):重置信息（None）</p>
<p>indexation(string):付息方式（None）</p>
</div></blockquote>
<p>}]</p>
<dl>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">base</span><span class="o">.</span><span class="n">get_bond_cash_flow</span><span class="p">(</span><span class="s1">&#39;160017&#39;</span><span class="p">)</span>
<span class="go">[{&#39;key&#39;: &#39;1600171&#39;, &#39;bondsId&#39;: 3700037, &#39;cashflowNo&#39;: 1, &#39;currency&#39;: &#39;CNY&#39;, &#39;cashflowType&#39;: &#39;R&#39;,</span>
<span class="go">&#39;cashflowStatus&#39;: &#39;F&#39;, &#39;dealType&#39;: &#39;P&#39;, &#39;cashflowDate&#39;: 20230830, &#39;cashflowTime&#39;: 175338,</span>
<span class="go">&#39;amount&#39;: -100.0, &#39;notional&#39;: 100.0, &#39;paymentDate&#39;: 20160804, &#39;exDivideneDate&#39;: 0, &#39;adjustDate&#39;: &#39;N&#39;,</span>
<span class="go">&#39;startDate&#39;: 20160804, &#39;endDate&#39;: 20160804, &#39;startDateAdj&#39;: 20160804, &#39;endDateAdj&#39;: 20160804, &#39;period&#39;: 1,</span>
<span class="go">&#39;phase&#39;: &#39;B&#39;, &#39;basis&#39;: &#39;&#39;, &#39;auditTimeStamp&#39;: &#39;2023-08-30 17:53:38.0&#39;, &#39;resets&#39;: None, &#39;indexation&#39;: None},</span>
<span class="go">{&#39;key&#39;: &#39;16001710&#39;, &#39;bondsId&#39;: 3700037, &#39;cashflowNo&#39;: 10, &#39;currency&#39;: &#39;CNY&#39;, &#39;cashflowType&#39;: &#39;I&#39;,</span>
<span class="go">&#39;cashflowStatus&#39;: &#39;F&#39;, &#39;dealType&#39;: &#39;R&#39;, &#39;cashflowDate&#39;: 20230830, &#39;cashflowTime&#39;: 175338, &#39;amount&#39;: 1.37,</span>
<span class="go">&#39;notional&#39;: 100.0, &#39;paymentDate&#39;: 20210204, &#39;exDivideneDate&#39;: 20210204, &#39;adjustDate&#39;: &#39;N&#39;,</span>
<span class="go">&#39;startDate&#39;: 20200804, &#39;endDate&#39;: 20210204, &#39;startDateAdj&#39;: 20200804, &#39;endDateAdj&#39;: 20210204, &#39;period&#39;: 10,</span>
<span class="go">&#39;phase&#39;: &#39;M&#39;, &#39;basis&#39;: &#39;F&#39;, &#39;auditTimeStamp&#39;: &#39;2023-08-30 17:53:38.0&#39;, &#39;resets&#39;: None, &#39;indexation&#39;: None}]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">base</span><span class="o">.</span><span class="n">get_bond_cash_flow</span><span class="p">(</span><span class="s1">&#39;160017&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="base.get_bond_info">
<span class="sig-prename descclassname"><span class="pre">base.</span></span><span class="sig-name descname"><span class="pre">get_bond_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#base.get_bond_info" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>债券基本信息查询</p>
</div></blockquote>
<dl>
<dt>参数:</dt><dd><p>symbol(str):债券编码</p>
</dd>
<dt>返回:</dt><dd><p>债券基础信息</p>
<p>Returns:dict</p>
<dl>
<dt>{</dt><dd><p>bondCode(str): 债券编码</p>
<p>couponFrequency(str): 付息频率</p>
<blockquote>
<div><p>支持枚举 [1D-每日, 1W-每周, 2W-每两周, 1M-每月, 3M-每季度, 6M-每半年, 1Y-每年. MT-利随本清, N-无]</p>
</div></blockquote>
<p>fixingRate(float): 票面利率</p>
<p>maturityDate(int): 到期日</p>
<p>valueDate(int): 起息日</p>
</dd>
</dl>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">base</span><span class="o">.</span><span class="n">get_bond_info</span><span class="p">(</span><span class="s1">&#39;160017_T+1&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="base.get_bull_bear_flag">
<span class="sig-prename descclassname"><span class="pre">base.</span></span><span class="sig-name descname"><span class="pre">get_bull_bear_flag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#base.get_bull_bear_flag" title="Link to this definition"></a></dt>
<dd><p>获取牛熊标识</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> (<em>string</em>) – 牛熊参数名称 (仅订阅一个牛熊指标时可不传)</p>
</dd>
</dl>
<p>返回: 牛熊标识,0 - 未知; 1 - 熊; 2 - 牛; 3 - 已达可能熊; 4 - 已达可能牛</p>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">bull_bear</span> <span class="o">=</span> <span class="n">base</span><span class="o">.</span><span class="n">get_bull_bear_flag</span><span class="p">(</span><span class="s1">&#39;牛熊参数&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="base.get_contract">
<span class="sig-prename descclassname"><span class="pre">base.</span></span><span class="sig-name descname"><span class="pre">get_contract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#base.get_contract" title="Link to this definition"></a></dt>
<dd><p>获取合约信息</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>code</strong> (<em>string</em>) – 合约唯一代码，合约名</p>
</dd>
</dl>
<p>Returns:dict
{</p>
<blockquote>
<div><p>code(string):合约代码</p>
<p>contractType(string):合约类型 ContractTypeEnum {B:基础合约，D:基差合约,T:期差合约,S:连续合约,M:月份合约,N:非标准合约}</p>
<p>sites(string):交易主体</p>
<p>productBroad(string):产品大类</p>
<p>products(string):产品小类</p>
<p>contractMultiplier(float):合约乘数</p>
<p>tenor(string):期限</p>
<p>tenorGroup(string):组合期限</p>
<p>lastDate(string):最后交易日</p>
<p>dealTypeGroup(string):组合交易品种</p>
<p>dealType(string):交易品种</p>
<p>valueDateRule(string):起息日规则</p>
<p>market(string):交易市场</p>
<p>quoteCurrency(string):报价货币</p>
<p>localName(string):本地名称</p>
<p>name(string):英文名称</p>
<p>quoteUnit(string):报价单位</p>
<p>startDate(string):开始交易日</p>
<p>noDecimal(int):报价有效位数</p>
<p>status(string):合约状态</p>
</div></blockquote>
<p>}</p>
<dl>
<dt>使用方法</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">qlog</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="n">base</span><span class="o">.</span><span class="n">get_contract</span><span class="p">(</span><span class="s2">&quot;EURUSDSP&quot;</span><span class="p">))</span>
<span class="go">{&#39;code&#39;: &#39;EURUSDSP&#39;, &#39;contractType&#39;: &#39;S&#39;, &#39;sites&#39;: &#39;BOC&#39;, &#39;productBroad&#39;: &#39;FX&#39;, &#39;products&#39;: &#39;FXSPOT&#39;,</span>
<span class="go">&#39;contractMultiplier&#39;: 100000.0, &#39;tenor&#39;: &#39;SPOT&#39;, &#39;id&#39;: 0, &#39;tenorGroup&#39;: None, &#39;lastDate&#39;: 20260701,</span>
<span class="go">&#39;comments&#39;: None, &#39;dealTypeGroup&#39;: None, &#39;globalId&#39;: None, &#39;quoteUnitCodifiersGrpCode&#39;: None, &#39;dealType&#39;: &#39;EURUSD&#39;,</span>
<span class="go">&#39;valueDateRule&#39;: &#39;T+2&#39;, &#39;market&#39;: &#39;360T_GTX_QDM,360T_SST_QDM,CFETS_LC_ODM,CFETS_LC_QDM,FXALL_QDM,JPMC,UBS&#39;,</span>
<span class="go">&#39;quoteCurrency&#39;: &#39;USD&#39;, &#39;timeStamp&#39;: &#39;2021-07-15 10:08:31.0&#39;, &#39;localName&#39;: &#39;EURUSDSP&#39;, &#39;name&#39;: &#39;EURUSDSP&#39;, &#39;quoteUnit&#39;: &#39;0&#39;,</span>
<span class="go">&#39;startDate&#39;: 20170701, &#39;noDecimal&#39;: 6, &#39;status&#39;: &#39;V&#39;}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">qlog</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="n">con</span><span class="p">[</span><span class="s1">&#39;code&#39;</span><span class="p">])</span>
<span class="go">EURUSDSP</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="base.query_coupon_pool_bond_info">
<span class="sig-prename descclassname"><span class="pre">base.</span></span><span class="sig-name descname"><span class="pre">query_coupon_pool_bond_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coupon_pool_code</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#base.query_coupon_pool_bond_info" title="Link to this definition"></a></dt>
<dd><blockquote>
<div><p>查询券池中的债券基本信息查询</p>
</div></blockquote>
<dl>
<dt>参数:</dt><dd><p>coupon_pool_code(str):券池编码</p>
</dd>
<dt>返回:</dt><dd><p>债券基础信息</p>
<p>Returns:dict</p>
<dl>
<dt>{   bondCode(str): {</dt><dd><p>bondCode(str): 债券编码</p>
<p>couponFrequency(str): 付息频率</p>
<blockquote>
<div><p>支持枚举 [1D-每日, 1W-每周, 2W-每两周, 1M-每月, 3M-每季度, 6M-每半年, 1Y-每年. MT-利随本清, N-无]</p>
</div></blockquote>
<p>fixingRate(float): 票面利率</p>
<p>termtomaturity(float): 待偿期</p>
<p>maturityDate(int): 到期日</p>
<p>valueDate(int): 起息日</p>
<p>}</p>
</dd>
</dl>
<p>}</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">base</span><span class="o">.</span><span class="n">query_coupon_pool_bond_info</span><span class="p">(</span><span class="s1">&#39;国债&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="quick_start.html" class="btn btn-neutral float-left" title="快速开始" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="public.html" class="btn btn-neutral float-right" title="公共方法" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>