<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>交易API &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="MT4API" href="mt4.html" />
    <link rel="prev" title="指标API" href="indicator_arsenals.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="arsenals_method.html">武器库</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="indicator_arsenals.html">指标API</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">交易API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.check_spread"><code class="docutils literal notranslate"><span class="pre">check_spread()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.close_all"><code class="docutils literal notranslate"><span class="pre">close_all()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.get_count_orders"><code class="docutils literal notranslate"><span class="pre">get_count_orders()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.init_count_orders"><code class="docutils literal notranslate"><span class="pre">init_count_orders()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.open_buy"><code class="docutils literal notranslate"><span class="pre">open_buy()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#trade_arsenals.open_sell"><code class="docutils literal notranslate"><span class="pre">open_sell()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="mt4.html">MT4API</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="arsenals_method.html">武器库</a></li>
      <li class="breadcrumb-item active">交易API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/trade_arsenals.rst.txt" rel="nofollow"> 查看页面源码</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-trade_arsenals">
<span id="api"></span><h1>交易API<a class="headerlink" href="#module-trade_arsenals" title="Link to this heading"></a></h1>
<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.check_spread">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">check_spread</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ask</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bid</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">point</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">point_limit</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.check_spread" title="Link to this definition"></a></dt>
<dd><p>买卖点差检查</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>上下文信息</strong> (<em>context         dict</em>) – </p></li>
<li><p><strong>买价</strong> (<em>ask             float</em>) – </p></li>
<li><p><strong>卖价</strong> (<em>bid             float</em>) – </p></li>
<li><p><strong>行情基点</strong> (<em>point           float</em>) – </p></li>
<li><p><strong>买卖最大价差</strong> (<em>point_limit     float</em>) – </p></li>
</ul>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:bool</p>
<p>点差检查结果</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">flag</span> <span class="o">=</span> <span class="n">trade_arsenals</span><span class="o">.</span><span class="n">check_spread</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">ask</span><span class="p">,</span> <span class="n">bid</span><span class="p">,</span> <span class="n">point</span><span class="p">,</span> <span class="n">point_limit</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.close_all">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">close_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bid</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ask</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">keep_side</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.close_all" title="Link to this definition"></a></dt>
<dd><p>逐笔订单全部平仓</p>
<dl>
<dt>必填参数:</dt><dd><p>context:  dict   上下文信息：必须包含以下字段</p>
<blockquote>
<div><p>symbol(str):合约品种</p>
<p>source(str):渠道,平仓渠道</p>
<p>slippage(float):滑点)</p>
<p>digits(int):报价有效位数</p>
</div></blockquote>
<p>bid:持有多仓的卖价</p>
<p>ask:持有空仓的买价</p>
</dd>
<dt>非必填参数:</dt><dd><p>keep_side:为头寸保留方向:B买;S卖</p>
</dd>
<dt>返回:</dt><dd><p>Returns:无</p>
</dd>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">trade_arsenals</span><span class="o">.</span><span class="n">close_all</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">bid</span><span class="p">,</span> <span class="n">ask</span><span class="p">,</span> <span class="n">keep_side</span><span class="o">=</span><span class="s1">&#39;B&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.get_count_orders">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">get_count_orders</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.get_count_orders" title="Link to this definition"></a></dt>
<dd><p>获取逐笔模式开仓累计对象</p>
<p>Keyword Arguments:</p>
<p>返回:</p>
<blockquote>
<div><p>Returns:object</p>
<p>{</p>
<p>buy_num(int):未平仓多单数量(累计)</p>
<p>sell_num(int):未平仓空单数量(累计)</p>
<p>buy_lots_all(int):未平仓多单总手数(累计)</p>
<p>sell_lots_all(int):未平仓空单总手数(累计)</p>
<p>buy_lots_one(int):未平仓多单手数(最后一单)</p>
<p>sell_lots_one(int):未平仓空单手数(最后一单)</p>
<p>buy_prof(float):未平仓多单盈亏(浮动盈亏)</p>
<p>sell_prof(float):未平仓空单盈亏(浮动盈亏)</p>
<p>buy_avg(float):未平仓多单盈亏平衡点</p>
<p>sell_avg(float):未平仓空单盈亏平衡点</p>
<p>buy_price(float):多单开单价(最后一单)</p>
<p>sell_price(float):空单开单价(最后一单)</p>
<p>buy_id(int):多单id号(最后一单)</p>
<p>sell_id(int):空单id号(最后一单)</p>
<p>}</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">count_orders</span> <span class="o">=</span> <span class="n">trade_arsenals</span><span class="o">.</span><span class="n">get_count_orders</span><span class="p">()</span>
<span class="go">    必须先事先调用 trade_arsenals.init_count_orders()</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.init_count_orders">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">init_count_orders</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.init_count_orders" title="Link to this definition"></a></dt>
<dd><p>逐笔模式开仓累计对象初始化</p>
<p>Keyword Arguments:</p>
<p>返回:</p>
<blockquote>
<div><p>Returns:object</p>
<p>{</p>
<p>buy_num(int):未平仓多单数量(累计)</p>
<p>sell_num(int):未平仓空单数量(累计)</p>
<p>buy_lots_all(int):未平仓多单总手数(累计)</p>
<p>sell_lots_all(int):未平仓空单总手数(累计)</p>
<p>buy_lots_one(int):未平仓多单手数(最后一单)</p>
<p>sell_lots_one(int):未平仓空单手数(最后一单)</p>
<p>buy_prof(float):未平仓多单盈亏(浮动盈亏)</p>
<p>sell_prof(float):未平仓空单盈亏(浮动盈亏)</p>
<p>buy_avg(float):未平仓多单盈亏平衡点</p>
<p>sell_avg(float):未平仓空单盈亏平衡点</p>
<p>buy_price(float):多单开单价(最后一单)</p>
<p>sell_price(float):空单开单价(最后一单)</p>
<p>buy_id(int):多单id号(最后一单)</p>
<p>sell_id(int):空单id号(最后一单)</p>
<p>}</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">count_orders</span> <span class="o">=</span> <span class="n">trade_arsenals</span><span class="o">.</span><span class="n">init_count_orders</span><span class="p">()</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.open_buy">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">open_buy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.open_buy" title="Link to this definition"></a></dt>
<dd><p>开仓买入</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>上下文信息：必须包含以下字段</strong> (<em>context     dict</em>) – <p>symbol(合约品种)</p>
<p>source(渠道)开仓渠道</p>
<p>slippage(滑点)</p>
<p>digits(报价有效位数)</p>
<p>quantity(下单量)</p>
</p></li>
<li><p><strong>开仓价</strong> (<em>price     float</em>) – </p></li>
</ul>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:str</p>
<p>订单id</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">order_id</span> <span class="o">=</span> <span class="n">trade_arsenals</span><span class="o">.</span><span class="n">open_buy</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">price</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="trade_arsenals.open_sell">
<span class="sig-prename descclassname"><span class="pre">trade_arsenals.</span></span><span class="sig-name descname"><span class="pre">open_sell</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">price</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#trade_arsenals.open_sell" title="Link to this definition"></a></dt>
<dd><p>开仓卖出</p>
<dl class="field-list simple">
<dt class="field-odd">关键字参数<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>上下文信息：必须包含以下字段</strong> (<em>context     dict</em>) – <p>symbol(合约品种)</p>
<p>source(渠道)开仓渠道</p>
<p>slippage(滑点)</p>
<p>digits(报价有效位数)</p>
<p>quantity(下单量)</p>
</p></li>
<li><p><strong>开仓价</strong> (<em>price     float</em>) – </p></li>
</ul>
</dd>
</dl>
<p>返回:</p>
<blockquote>
<div><p>Returns:str</p>
<p>订单id</p>
</div></blockquote>
<dl>
<dt>使用方法:</dt><dd><div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">order_id</span> <span class="o">=</span> <span class="n">trade_arsenals</span><span class="o">.</span><span class="n">open_sell</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="n">price</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="页脚">
        <a href="indicator_arsenals.html" class="btn btn-neutral float-left" title="指标API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="mt4.html" class="btn btn-neutral float-right" title="MT4API" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>