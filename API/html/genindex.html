<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>索引 &mdash; quantapi 1.3.0 文档</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=4ebe3ea6"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=beaddf03"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="#" />
    <link rel="search" title="搜索" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            quantapi
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="搜索文档" aria-label="搜索文档" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="导航菜单">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="quick_start.html">快速开始</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="base.html">基础API</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="public.html">公共方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="trade_method.html">交易方法</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="event.html">交易事件</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enum_stu.html">常用枚举和结构</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="arsenals_method.html">武器库</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="talib_erayt.html">指标</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="移动版导航菜单" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">quantapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="页面导航">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">索引</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">索引</h1>

<div class="genindex-jumpbox">
 <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#K"><strong>K</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#固"><strong>固</strong></a>
 | <a href="#外"><strong>外</strong></a>
 
</div>
<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="object.html#object.BarDataList">BarDataList（object 中的类）</a>
</li>
      <li>
    base

      <ul>
        <li><a href="base.html#module-base">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="indicator_arsenals.html#indicator_arsenals.BULL_BEAR">BULL_BEAR()（在 indicator_arsenals 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="bond/md.html#md.cal_bond_yield_curve_indicator">cal_bond_yield_curve_indicator()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/deal.html#deal.cancel_order">cancel_order()（在 固收.deal 模块中）</a>
</li>
      <li><a href="fx/deal.html#deal.cancel_order">cancel_order()（在 外汇.deal 模块中）</a>
</li>
      <li><a href="talib_erayt.html#talib_erayt.CCI">CCI()（在 talib_erayt 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="trade_arsenals.html#trade_arsenals.check_spread">check_spread()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="trade_arsenals.html#trade_arsenals.close_all">close_all()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.ContractStatusEnum">ContractStatusEnum（enum_erayt 中的类）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.ContractTypeEnum">ContractTypeEnum（enum_erayt 中的类）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    date

      <ul>
        <li><a href="date.html#module-date">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="enum_erayt.html#enum_erayt.EffectEnum">EffectEnum（enum_erayt 中的类）</a>
</li>
      <li>
    enum_erayt

      <ul>
        <li><a href="enum_erayt.html#module-enum_erayt">module</a>
</li>
      </ul></li>
      <li>
    equantapi.xrisk

      <ul>
        <li><a href="xrisk.html#module-equantapi.xrisk">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="indicator_arsenals.html#indicator_arsenals.ERAYTMA">ERAYTMA()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="qlog.html#qlog.error">error()（在 qlog 模块中）</a>
</li>
      <li><a href="qlog.html#qlog.error_f">error_f()（在 qlog 模块中）</a>
</li>
      <li>
    event

      <ul>
        <li><a href="event.html#module-event">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="indicator_arsenals.html#indicator_arsenals.FibonacciMA">FibonacciMA()（在 indicator_arsenals 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    funds

      <ul>
        <li><a href="funds.html#module-funds">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="param.html#param.get">get()（在 param 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_active_bond">get_active_bond()（在 固收.md 模块中）</a>
</li>
      <li><a href="base.html#base.get_bond_cash_flow">get_bond_cash_flow()（在 base 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_bond_dimension_loss_profit">get_bond_dimension_loss_profit()（在 固收.pos 模块中）</a>
</li>
      <li><a href="base.html#base.get_bond_info">get_bond_info()（在 base 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_bond_loss_profit">get_bond_loss_profit()（在 固收.pos 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_bond_mutual_calculation">get_bond_mutual_calculation()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_bond_residual_curve">get_bond_residual_curve()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_bond_yield_curve">get_bond_yield_curve()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_bond_yield_curve_slope">get_bond_yield_curve_slope()（在 固收.md 模块中）</a>
</li>
      <li><a href="base.html#base.get_bull_bear_flag">get_bull_bear_flag()（在 base 模块中）</a>
</li>
      <li><a href="date.html#date.get_bus_day">get_bus_day()（在 date 模块中）</a>
</li>
      <li><a href="date.html#date.get_bus_time">get_bus_time()（在 date 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_cn_bond_evaluation">get_cn_bond_evaluation()（在 固收.md 模块中）</a>
</li>
      <li><a href="base.html#base.get_contract">get_contract()（在 base 模块中）</a>
</li>
      <li><a href="trade_arsenals.html#trade_arsenals.get_count_orders">get_count_orders()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_depth_info_quoteid">get_depth_info_quoteid()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_dv01">get_dv01()（在 固收.pos 模块中）</a>
</li>
      <li><a href="funds.html#funds.get_funds">get_funds()（在 funds 模块中）</a>
</li>
      <li><a href="fx/pos.html#pos.get_fx_exposure">get_fx_exposure()（在 外汇.pos 模块中）</a>
</li>
      <li><a href="fx/pos.html#pos.get_fxpm_exposure">get_fxpm_exposure()（在 外汇.pos 模块中）</a>
</li>
      <li><a href="param.html#param.get_group_names">get_group_names()（在 param 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_indicators">get_indicators()（在 固收.pos 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="bond/md.html#md.get_irs_df">get_irs_df()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_irs_fixing_curve">get_irs_fixing_curve()（在 固收.md 模块中）</a>
</li>
      <li><a href="funds.html#funds.get_market_info">get_market_info()（在 funds 模块中）</a>
</li>
      <li><a href="param.html#param.get_matrix_np">get_matrix_np()（在 param 模块中）</a>
</li>
      <li><a href="param.html#param.get_matrix_pb">get_matrix_pb()（在 param 模块中）</a>
</li>
      <li><a href="date.html#date.get_open_close_market">get_open_close_market()（在 date 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_ord_position">get_ord_position()（在 固收.pos 模块中）</a>
</li>
      <li><a href="fx/pos.html#pos.get_ord_position">get_ord_position()（在 外汇.pos 模块中）</a>
</li>
      <li><a href="bond/deal.html#deal.get_order">get_order()（在 固收.deal 模块中）</a>
</li>
      <li><a href="fx/deal.html#deal.get_order">get_order()（在 外汇.deal 模块中）</a>
</li>
      <li><a href="bond/deal.html#deal.get_orders">get_orders()（在 固收.deal 模块中）</a>
</li>
      <li><a href="fx/deal.html#deal.get_orders">get_orders()（在 外汇.deal 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_position">get_position()（在 固收.pos 模块中）</a>
</li>
      <li><a href="fx/pos.html#pos.get_position">get_position()（在 外汇.pos 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_position_onroad">get_position_onroad()（在 固收.pos 模块中）</a>
</li>
      <li><a href="fx/pos.html#pos.get_position_onroad">get_position_onroad()（在 外汇.pos 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_price">get_price()（在 固收.md 模块中）</a>
</li>
      <li><a href="fx/md.html#md.get_price">get_price()（在 外汇.md 模块中）</a>
</li>
      <li><a href="date.html#date.get_sys_time">get_sys_time()（在 date 模块中）</a>
</li>
      <li><a href="date.html#date.get_timestamp">get_timestamp()（在 date 模块中）</a>
</li>
      <li><a href="bond/md.html#md.get_xswap_curve">get_xswap_curve()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.get_xswap_loss_profit">get_xswap_loss_profit()（在 固收.pos 模块中）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.GMMA">GMMA()（在 indicator_arsenals 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="enum_erayt.html#enum_erayt.HedgeFlagEnum">HedgeFlagEnum（enum_erayt 中的类）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="bond/md.html#md.inactiveBondPricing">inactiveBondPricing()（在 固收.md 模块中）</a>
</li>
      <li>
    indicator_arsenals

      <ul>
        <li><a href="indicator_arsenals.html#module-indicator_arsenals">module</a>
</li>
      </ul></li>
      <li><a href="qlog.html#qlog.info">info()（在 qlog 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="qlog.html#qlog.info_f">info_f()（在 qlog 模块中）</a>
</li>
      <li><a href="event.html#event.init">init()（在 event 模块中）</a>
</li>
      <li><a href="trade_arsenals.html#trade_arsenals.init_count_orders">init_count_orders()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.InOutMarketEnum">InOutMarketEnum（enum_erayt 中的类）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="talib_erayt.html#talib_erayt.KELCHAN">KELCHAN()（在 talib_erayt 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="talib_erayt.html#talib_erayt.MA">MA()（在 talib_erayt 模块中）</a>
</li>
      <li><a href="talib_erayt.html#talib_erayt.MACD">MACD()（在 talib_erayt 模块中）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.MACDBOLL">MACDBOLL()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="object.html#object.MarketDataList">MarketDataList（object 中的类）</a>
</li>
      <li><a href="talib_erayt.html#talib_erayt.MFI">MFI()（在 talib_erayt 模块中）</a>
</li>
      <li>
    module

      <ul>
        <li><a href="base.html#module-base">base</a>
</li>
        <li><a href="date.html#module-date">date</a>
</li>
        <li><a href="enum_erayt.html#module-enum_erayt">enum_erayt</a>
</li>
        <li><a href="xrisk.html#module-equantapi.xrisk">equantapi.xrisk</a>
</li>
        <li><a href="event.html#module-event">event</a>
</li>
        <li><a href="funds.html#module-funds">funds</a>
</li>
        <li><a href="indicator_arsenals.html#module-indicator_arsenals">indicator_arsenals</a>
</li>
        <li><a href="object.html#module-object">object</a>
</li>
        <li><a href="param.html#module-param">param</a>
</li>
        <li><a href="qlog.html#module-qlog">qlog</a>
</li>
        <li><a href="quick_start.html#module-quick_start">quick_start</a>
</li>
        <li><a href="scheduler.html#module-scheduler">scheduler</a>
</li>
        <li><a href="talib_erayt.html#module-talib_erayt">talib_erayt</a>
</li>
        <li><a href="trade_arsenals.html#module-trade_arsenals">trade_arsenals</a>
</li>
        <li><a href="bond/deal.html#module-.deal">固收.deal</a>
</li>
        <li><a href="bond/maker.html#module-.maker">固收.maker</a>
</li>
        <li><a href="bond/md.html#module-.md">固收.md</a>
</li>
        <li><a href="bond/pos.html#module-.pos">固收.pos</a>
</li>
        <li><a href="bond/signal.html#module-.signal">固收.signal</a>
</li>
        <li><a href="fx/deal.html#module-.deal">外汇.deal</a>
</li>
        <li><a href="fx/maker.html#module-.maker">外汇.maker</a>
</li>
        <li><a href="fx/md.html#module-.md">外汇.md</a>
</li>
        <li><a href="fx/pos.html#module-.pos">外汇.pos</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="talib_erayt.html#talib_erayt.MOM">MOM()（在 talib_erayt 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    object

      <ul>
        <li><a href="object.html#module-object">module</a>
</li>
      </ul></li>
      <li><a href="talib_erayt.html#talib_erayt.OBV">OBV()（在 talib_erayt 模块中）</a>
</li>
      <li><a href="event.html#event.onBusinessDate">onBusinessDate()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onData">onData()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onOrder">onOrder()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onQuote">onQuote()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onQuoteOrder">onQuoteOrder()（在 event 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="event.html#event.onRfqQuote">onRfqQuote()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onRfqQuoteOrder">onRfqQuoteOrder()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onRfqReq">onRfqReq()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onTime">onTime()（在 event 模块中）</a>
</li>
      <li><a href="event.html#event.onTrade">onTrade()（在 event 模块中）</a>
</li>
      <li><a href="trade_arsenals.html#trade_arsenals.open_buy">open_buy()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="trade_arsenals.html#trade_arsenals.open_sell">open_sell()（在 trade_arsenals 模块中）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.OrderStatusEnum">OrderStatusEnum（enum_erayt 中的类）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.OrderTypeEnum">OrderTypeEnum（enum_erayt 中的类）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    param

      <ul>
        <li><a href="param.html#module-param">module</a>
</li>
      </ul></li>
      <li><a href="enum_erayt.html#enum_erayt.PosSideEnum">PosSideEnum（enum_erayt 中的类）</a>
</li>
      <li><a href="enum_erayt.html#enum_erayt.PosTypeEnum">PosTypeEnum（enum_erayt 中的类）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.PP_Camarilla">PP_Camarilla()（在 indicator_arsenals 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="indicator_arsenals.html#indicator_arsenals.PP_DeMarks">PP_DeMarks()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.PP_Fibonacci">PP_Fibonacci()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.PP_Standard">PP_Standard()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.PP_Woodie">PP_Woodie()（在 indicator_arsenals 模块中）</a>
</li>
      <li><a href="bond/md.html#md.put_maker_symbol">put_maker_symbol()（在 固收.md 模块中）</a>
</li>
      <li><a href="fx/md.html#md.put_maker_symbol">put_maker_symbol()（在 外汇.md 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    qlog

      <ul>
        <li><a href="qlog.html#module-qlog">module</a>
</li>
      </ul></li>
      <li><a href="bond/md.html#md.query_bar_spreads">query_bar_spreads()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.query_bars">query_bars()（在 固收.md 模块中）</a>
</li>
      <li><a href="fx/md.html#md.query_bars">query_bars()（在 外汇.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.query_bars_pro">query_bars_pro()（在 固收.md 模块中）</a>
</li>
      <li><a href="fx/md.html#md.query_bars_pro">query_bars_pro()（在 外汇.md 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="base.html#base.query_coupon_pool_bond_info">query_coupon_pool_bond_info()（在 base 模块中）</a>
</li>
      <li><a href="bond/pos.html#pos.query_coupon_pool_indicators">query_coupon_pool_indicators()（在 固收.pos 模块中）</a>
</li>
      <li><a href="bond/md.html#md.query_coupons_pool_bars">query_coupons_pool_bars()（在 固收.md 模块中）</a>
</li>
      <li><a href="bond/md.html#md.query_spreads">query_spreads()（在 固收.md 模块中）</a>
</li>
      <li>
    quick_start

      <ul>
        <li><a href="quick_start.html#module-quick_start">module</a>
</li>
      </ul></li>
      <li><a href="enum_erayt.html#enum_erayt.QuoteStatusEnum">QuoteStatusEnum（enum_erayt 中的类）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="xrisk.html#equantapi.xrisk.risk_param_init">risk_param_init()（在 equantapi.xrisk 模块中）</a>
</li>
      <li><a href="talib_erayt.html#talib_erayt.RSI">RSI()（在 talib_erayt 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="scheduler.html#scheduler.run_daily">run_daily()（在 scheduler 模块中）</a>
</li>
      <li><a href="scheduler.html#scheduler.run_second">run_second()（在 scheduler 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    scheduler

      <ul>
        <li><a href="scheduler.html#module-scheduler">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="enum_erayt.html#enum_erayt.SideEnum">SideEnum（enum_erayt 中的类）</a>
</li>
      <li><a href="talib_erayt.html#talib_erayt.STOCH">STOCH()（在 talib_erayt 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    talib_erayt

      <ul>
        <li><a href="talib_erayt.html#module-talib_erayt">module</a>
</li>
      </ul></li>
      <li><a href="xrisk.html#equantapi.xrisk.than_max_loss">than_max_loss()（在 equantapi.xrisk 模块中）</a>
</li>
      <li><a href="xrisk.html#equantapi.xrisk.than_today_max_loss">than_today_max_loss()（在 equantapi.xrisk 模块中）</a>
</li>
      <li><a href="xrisk.html#equantapi.xrisk.than_today_max_orders">than_today_max_orders()（在 equantapi.xrisk 模块中）</a>
</li>
      <li><a href="xrisk.html#equantapi.xrisk.than_today_max_undo_orders">than_today_max_undo_orders()（在 equantapi.xrisk 模块中）</a>
</li>
      <li><a href="bond/deal.html#deal.to_order">to_order()（在 固收.deal 模块中）</a>
</li>
      <li><a href="fx/deal.html#deal.to_order">to_order()（在 外汇.deal 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_quote">to_quote()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_quote">to_quote()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_quote_cancel">to_quote_cancel()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_quote_cancel">to_quote_cancel()（在 外汇.maker 模块中）</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="fx/maker.html#maker.to_quote_order_confirm">to_quote_order_confirm()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_rfq_quote">to_rfq_quote()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_rfq_quote">to_rfq_quote()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_rfq_quote_cancel">to_rfq_quote_cancel()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_rfq_quote_cancel">to_rfq_quote_cancel()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_rfq_quote_order_confirm">to_rfq_quote_order_confirm()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_rfq_quote_order_confirm">to_rfq_quote_order_confirm()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/maker.html#maker.to_rfq_quote_rej">to_rfq_quote_rej()（在 固收.maker 模块中）</a>
</li>
      <li><a href="fx/maker.html#maker.to_rfq_quote_rej">to_rfq_quote_rej()（在 外汇.maker 模块中）</a>
</li>
      <li><a href="bond/signal.html#signal.to_signal">to_signal()（在 固收.signal 模块中）</a>
</li>
      <li>
    trade_arsenals

      <ul>
        <li><a href="trade_arsenals.html#module-trade_arsenals">module</a>
</li>
      </ul></li>
      <li><a href="indicator_arsenals.html#indicator_arsenals.TRADMA">TRADMA()（在 indicator_arsenals 模块中）</a>
</li>
  </ul></td>
</tr></table>

<h2 id="固">固</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    固收.deal

      <ul>
        <li><a href="bond/deal.html#module-.deal">module</a>
</li>
      </ul></li>
      <li>
    固收.maker

      <ul>
        <li><a href="bond/maker.html#module-.maker">module</a>
</li>
      </ul></li>
      <li>
    固收.md

      <ul>
        <li><a href="bond/md.html#module-.md">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    固收.pos

      <ul>
        <li><a href="bond/pos.html#module-.pos">module</a>
</li>
      </ul></li>
      <li>
    固收.signal

      <ul>
        <li><a href="bond/signal.html#module-.signal">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="外">外</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    外汇.deal

      <ul>
        <li><a href="fx/deal.html#module-.deal">module</a>
</li>
      </ul></li>
      <li>
    外汇.maker

      <ul>
        <li><a href="fx/maker.html#module-.maker">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    外汇.md

      <ul>
        <li><a href="fx/md.html#module-.md">module</a>
</li>
      </ul></li>
      <li>
    外汇.pos

      <ul>
        <li><a href="fx/pos.html#module-.pos">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; 版权所有 2023, erayt。</p>
  </div>

  利用 <a href="https://www.sphinx-doc.org/">Sphinx</a> 构建，使用的 
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">主题</a>
    由 <a href="https://readthedocs.org">Read the Docs</a> 开发.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>